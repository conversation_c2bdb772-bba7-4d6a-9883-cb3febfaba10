/**
 * 数据验证器
 * 用于验证API返回数据的完整性和正确性
 */

import { ApiFeedPost, ApiUser, ApiWorkoutDetail } from '../types/feed.types';

// 验证结果接口
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

// 数据完整性报告接口
export interface DataIntegrityReport {
  totalItems: number;
  validItems: number;
  invalidItems: number;
  missingFields: Record<string, number>;
  fieldCoverage: Record<string, number>;
  recommendations: string[];
}

/**
 * 验证API用户数据
 */
export function validateApiUser(user: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // 检查必需字段
  if (!user) {
    errors.push('用户对象为空');
    return { isValid: false, errors, warnings };
  }

  if (typeof user.id !== 'number') {
    errors.push('用户ID必须为数字');
  }

  if (!user.nickname || typeof user.nickname !== 'string') {
    errors.push('用户昵称不能为空且必须为字符串');
  }

  if (!user.avatar_url || typeof user.avatar_url !== 'string') {
    warnings.push('用户头像URL缺失或格式错误');
  }

  if (typeof user.gender !== 'number') {
    warnings.push('用户性别信息缺失');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证API训练详情数据
 */
export function validateApiWorkoutDetail(workoutDetail: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!workoutDetail) {
    warnings.push('训练详情为空');
    return { isValid: true, errors, warnings };
  }

  // 检查基本字段
  if (typeof workoutDetail.id !== 'number') {
    errors.push('训练详情ID必须为数字');
  }

  if (!workoutDetail.name || typeof workoutDetail.name !== 'string') {
    errors.push('训练名称不能为空');
  }

  if (!workoutDetail.workout_exercises || !Array.isArray(workoutDetail.workout_exercises)) {
    errors.push('训练动作列表必须为数组');
  } else {
    // 验证训练动作
    workoutDetail.workout_exercises.forEach((exercise: any, index: number) => {
      if (!exercise.exercise_name) {
        errors.push(`动作 ${index + 1} 缺少名称`);
      }
      if (typeof exercise.sets !== 'number' || exercise.sets <= 0) {
        errors.push(`动作 ${index + 1} 组数无效`);
      }
      if (!exercise.set_records || !Array.isArray(exercise.set_records)) {
        warnings.push(`动作 ${index + 1} 缺少详细记录`);
      }
    });
  }

  // 检查时间字段
  if (!workoutDetail.start_time) {
    warnings.push('缺少训练开始时间');
  }
  if (!workoutDetail.end_time) {
    warnings.push('缺少训练结束时间');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证API帖子数据
 */
export function validateApiFeedPost(post: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!post) {
    errors.push('帖子对象为空');
    return { isValid: false, errors, warnings };
  }

  // 检查必需字段
  if (typeof post.id !== 'number') {
    errors.push('帖子ID必须为数字');
  }

  if (!post.content || typeof post.content !== 'string') {
    errors.push('帖子内容不能为空');
  }

  if (!post.created_at || typeof post.created_at !== 'string') {
    errors.push('帖子创建时间不能为空');
  }

  // 验证用户信息
  const userValidation = validateApiUser(post.user);
  if (!userValidation.isValid) {
    errors.push(...userValidation.errors.map(err => `用户信息: ${err}`));
  }
  warnings.push(...userValidation.warnings.map(warn => `用户信息: ${warn}`));

  // 验证统计字段
  const statFields = ['like_count', 'comment_count', 'share_count', 'view_count', 'reported_count'];
  statFields.forEach(field => {
    if (field in post) {
      if (typeof post[field] !== 'number' || post[field] < 0) {
        errors.push(`统计字段 ${field} 必须为非负数字`);
      }
    } else {
      warnings.push(`缺少统计字段: ${field}`);
    }
  });

  // 验证布尔字段
  if ('is_liked_by_current_user' in post && typeof post.is_liked_by_current_user !== 'boolean') {
    errors.push('is_liked_by_current_user 必须为布尔值');
  }

  // 验证数组字段
  if (post.images && !Array.isArray(post.images)) {
    errors.push('images 必须为数组');
  }

  if (post.tags && !Array.isArray(post.tags)) {
    errors.push('tags 必须为数组');
  }

  if (post.comments_summary && !Array.isArray(post.comments_summary)) {
    errors.push('comments_summary 必须为数组');
  }

  // 验证训练详情（如果存在）
  if (post.related_workout_detail) {
    const workoutValidation = validateApiWorkoutDetail(post.related_workout_detail);
    if (!workoutValidation.isValid) {
      errors.push(...workoutValidation.errors.map(err => `训练详情: ${err}`));
    }
    warnings.push(...workoutValidation.warnings.map(warn => `训练详情: ${warn}`));
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 验证API响应数据
 */
export function validateApiFeedResponse(response: any): ValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (!response) {
    errors.push('响应数据为空');
    return { isValid: false, errors, warnings };
  }

  // 检查分页字段
  if (typeof response.total !== 'number') {
    errors.push('total 字段必须为数字');
  }

  if (!response.items || !Array.isArray(response.items)) {
    errors.push('items 字段必须为数组');
    return { isValid: false, errors, warnings };
  }

  // 验证每个帖子
  let validPostCount = 0;
  response.items.forEach((post: any, index: number) => {
    const postValidation = validateApiFeedPost(post);
    if (postValidation.isValid) {
      validPostCount++;
    } else {
      errors.push(`帖子 ${index + 1}: ${postValidation.errors.join(', ')}`);
    }
    
    if (postValidation.warnings.length > 0) {
      warnings.push(`帖子 ${index + 1}: ${postValidation.warnings.join(', ')}`);
    }
  });

  // 检查数据一致性
  if (response.total > 0 && response.items.length === 0) {
    warnings.push('总数大于0但items为空');
  }

  if (validPostCount < response.items.length) {
    warnings.push(`${response.items.length - validPostCount} 个帖子数据不完整`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * 生成数据完整性报告
 */
export function generateDataIntegrityReport(posts: any[]): DataIntegrityReport {
  const report: DataIntegrityReport = {
    totalItems: posts.length,
    validItems: 0,
    invalidItems: 0,
    missingFields: {},
    fieldCoverage: {},
    recommendations: []
  };

  // 定义期望的字段
  const expectedFields = [
    'id', 'title', 'content', 'user', 'created_at', 'updated_at',
    'like_count', 'comment_count', 'share_count', 'view_count', 'reported_count',
    'is_liked_by_current_user', 'images', 'tags', 'comments_summary',
    'related_workout_detail'
  ];

  posts.forEach(post => {
    const validation = validateApiFeedPost(post);
    
    if (validation.isValid) {
      report.validItems++;
    } else {
      report.invalidItems++;
    }

    // 统计字段覆盖率
    expectedFields.forEach(field => {
      if (field in post && post[field] !== null && post[field] !== undefined) {
        report.fieldCoverage[field] = (report.fieldCoverage[field] || 0) + 1;
      } else {
        report.missingFields[field] = (report.missingFields[field] || 0) + 1;
      }
    });
  });

  // 生成建议
  const coverageThreshold = 0.8; // 80%覆盖率阈值
  Object.entries(report.fieldCoverage).forEach(([field, count]) => {
    const coverage = count / posts.length;
    if (coverage < coverageThreshold) {
      report.recommendations.push(
        `字段 ${field} 覆盖率较低 (${(coverage * 100).toFixed(1)}%)，建议检查数据源`
      );
    }
  });

  if (report.invalidItems > 0) {
    report.recommendations.push(
      `发现 ${report.invalidItems} 个无效数据项，建议加强数据验证`
    );
  }

  if (Object.keys(report.missingFields).length > 0) {
    const topMissingFields = Object.entries(report.missingFields)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([field]) => field);
    
    report.recommendations.push(
      `最常缺失的字段: ${topMissingFields.join(', ')}`
    );
  }

  return report;
}

/**
 * 数据修复器 - 尝试修复常见的数据问题
 */
export function repairApiPostData(post: any): any {
  if (!post) return post;

  const repairedPost = { ...post };

  // 修复缺失的统计字段
  const statFields = ['like_count', 'comment_count', 'share_count', 'view_count', 'reported_count'];
  statFields.forEach(field => {
    if (!(field in repairedPost) || typeof repairedPost[field] !== 'number') {
      repairedPost[field] = 0;
    }
  });

  // 修复缺失的数组字段
  const arrayFields = ['images', 'tags', 'comments_summary'];
  arrayFields.forEach(field => {
    if (!(field in repairedPost) || !Array.isArray(repairedPost[field])) {
      repairedPost[field] = [];
    }
  });

  // 修复布尔字段
  if (!('is_liked_by_current_user' in repairedPost)) {
    repairedPost.is_liked_by_current_user = false;
  }

  // 修复用户信息
  if (repairedPost.user) {
    if (!repairedPost.user.avatar_url) {
      repairedPost.user.avatar_url = '/api/placeholder/40/40';
    }
    if (!repairedPost.user.nickname) {
      repairedPost.user.nickname = '未知用户';
    }
  }

  return repairedPost;
}

export default {
  validateApiUser,
  validateApiWorkoutDetail,
  validateApiFeedPost,
  validateApiFeedResponse,
  generateDataIntegrityReport,
  repairApiPostData
};
