/**
 * 数据转换器
 * 将API返回的数据格式转换为前端组件使用的格式
 */

import {
  ApiUser,
  ApiFeedPost,
  ApiWorkoutExercise,
  ApiSetRecord,
  ApiWorkoutDetail,
  FeedUser,
  FeedPost,
  WorkoutExercise,
  SetRecord,
  WorkoutData,
  CarouselItem
} from '../types/feed.types';
import { MuscleGroupEnum } from '../types/muscle.types';
import { 
  calculateMuscleIntensities, 
  generateStaticMuscleColorConfig 
} from './muscleColorCalculator';





/**
 * 从动作名称推断主要肌肉群
 */
function inferMuscleGroupsFromExerciseName(exerciseName: string): {
  primary: MuscleGroupEnum[];
  secondary: MuscleGroupEnum[];
} {
  const name = exerciseName.toLowerCase();
  const primary: MuscleGroupEnum[] = [];
  const secondary: MuscleGroupEnum[] = [];

  // 胸部动作
  if (name.includes('卧推') || name.includes('推举') || name.includes('飞鸟')) {
    primary.push(MuscleGroupEnum.CHEST);
    secondary.push(MuscleGroupEnum.TRICEPS, MuscleGroupEnum.SHOULDERS_FRONT);
  }
  // 背部动作
  else if (name.includes('划船') || name.includes('引体') || name.includes('下拉')) {
    primary.push(MuscleGroupEnum.BACK);
    secondary.push(MuscleGroupEnum.BICEPS);
  }
  // 肩部动作
  else if (name.includes('肩') || name.includes('推举')) {
    primary.push(MuscleGroupEnum.SHOULDERS_FRONT);
    secondary.push(MuscleGroupEnum.TRICEPS);
  }
  // 腿部动作
  else if (name.includes('深蹲') || name.includes('腿')) {
    primary.push(MuscleGroupEnum.QUADRICEPS);
    secondary.push(MuscleGroupEnum.GLUTES);
  }

  // 如果没有匹配到，设置默认值
  if (primary.length === 0) {
    primary.push(MuscleGroupEnum.CHEST); // 默认胸部
  }

  return { primary, secondary };
}

/**
 * API组记录转换器
 */
export const setRecordTransformer = {
  fromApi(apiSetRecord: ApiSetRecord): SetRecord {
    return {
      id: apiSetRecord.id.toString(),
      setNumber: apiSetRecord.set_number,
      setType: apiSetRecord.set_type,
      weight: apiSetRecord.weight,
      reps: apiSetRecord.reps,
      completed: apiSetRecord.completed,
      notes: apiSetRecord.notes || undefined
    };
  },

  toApi(setRecord: SetRecord): ApiSetRecord {
    return {
      id: parseInt(setRecord.id),
      workout_exercise_id: 0, // 需要在上层设置
      set_number: setRecord.setNumber,
      set_type: setRecord.setType,
      weight: setRecord.weight,
      reps: setRecord.reps,
      completed: setRecord.completed,
      notes: setRecord.notes || '',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
  }
};

/**
 * API训练动作转换器
 */
export const workoutExerciseTransformer = {
  fromApi(apiExercise: ApiWorkoutExercise): WorkoutExercise {
    // 转换组记录
    const setRecords = apiExercise.set_records?.map(record => 
      setRecordTransformer.fromApi(record)
    ) || [];

    // 从组记录计算总重量
    const totalWeight = setRecords.reduce((sum, record) => 
      sum + (record.weight * record.reps), 0
    );

    // 推断肌肉群
    const muscleGroups = inferMuscleGroupsFromExerciseName(apiExercise.exercise_name);

    return {
      id: apiExercise.id.toString(),
      name: apiExercise.exercise_name,
      image_url: apiExercise.exercise_image,
      sets: apiExercise.sets,
      reps: typeof apiExercise.reps === 'string' ? parseInt(apiExercise.reps.split(',')[0]) || 0 : apiExercise.reps,
      weight: totalWeight / Math.max(setRecords.length, 1), // 平均重量
      rest_time: apiExercise.rest_seconds,
      primary_muscles: muscleGroups.primary,
      secondary_muscles: muscleGroups.secondary,
      notes: apiExercise.notes || undefined,
      exercise_type: apiExercise.exercise_type,
      video_url: apiExercise.video_url,
      set_records: setRecords,
      order: apiExercise.order
    };
  },

  toApi(exercise: WorkoutExercise): ApiWorkoutExercise {
    return {
      id: parseInt(exercise.id),
      workout_id: 0, // 需要在上层设置
      exercise_id: 0, // 需要在上层设置
      exercise_name: exercise.name,
      exercise_image: exercise.image_url || '',
      exercise_description: null,
      video_url: exercise.video_url || '',
      sets: exercise.sets,
      reps: exercise.reps.toString(),
      rest_seconds: exercise.rest_time || 60,
      order: exercise.order || 1,
      notes: exercise.notes || null,
      exercise_type: exercise.exercise_type || 'weight_reps',
      superset_group: null,
      weight: exercise.weight,
      set_records: exercise.set_records?.map(record => 
        setRecordTransformer.toApi(record)
      ) || []
    };
  }
};

/**
 * API用户转换器
 */
export const userTransformer = {
  fromApi(apiUser: ApiUser): FeedUser {
    return {
      id: apiUser.id.toString(),
      name: apiUser.nickname || '未知用户',
      username: `user_${apiUser.id}`,
      avatar: apiUser.avatar_url || '/api/placeholder/40/40',
      isVerified: false // 暂时设为false，后续可根据需要调整
    };
  },

  toApi(user: FeedUser): ApiUser {
    return {
      id: parseInt(user.id),
      nickname: user.name,
      avatar_url: user.avatar,
      gender: 0,
      activity_level: 3,
      completed: false
    };
  }
};

/**
 * API训练详情转换器
 */
export const workoutDetailTransformer = {
  fromApi(apiWorkoutDetail: ApiWorkoutDetail): WorkoutData {
    const exercises = apiWorkoutDetail.workout_exercises.map(ex => 
      workoutExerciseTransformer.fromApi(ex)
    );

    // 计算总数据
    const totalSets = exercises.reduce((sum, ex) => sum + ex.sets, 0);
    const totalVolume = exercises.reduce((sum, ex) => 
      sum + (ex.weight * ex.sets * (typeof ex.reps === 'number' ? ex.reps : 10)), 0
    );

    // 计算持续时间（秒）
    const startTime = new Date(apiWorkoutDetail.start_time);
    const endTime = new Date(apiWorkoutDetail.end_time);
    const durationSeconds = Math.max(
      Math.floor((endTime.getTime() - startTime.getTime()) / 1000),
      apiWorkoutDetail.actual_duration || 0
    );

    // 计算肌肉强度
    const muscleIntensities = calculateMuscleIntensities(exercises);

    return {
      id: apiWorkoutDetail.id.toString(),
      name: apiWorkoutDetail.name,
      duration_seconds: durationSeconds,
      total_sets: totalSets,
      total_volume: totalVolume,
      calories_burned: Math.floor(totalVolume * 0.1), // 简单估算
      exercises,
      muscle_intensities: muscleIntensities,
      created_at: apiWorkoutDetail.created_at,
      status: apiWorkoutDetail.status,
      start_time: apiWorkoutDetail.start_time,
      end_time: apiWorkoutDetail.end_time
    };
  }
};

/**
 * 生成轮播图项目
 */
function generateCarouselItems(
  workoutData?: WorkoutData, 
  images?: string[]
): CarouselItem[] {
  const items: CarouselItem[] = [];

  // 添加肌肉示意图项
  if (workoutData && workoutData.muscle_intensities) {
    const selectedMuscles = workoutData.muscle_intensities.map(intensity => intensity.muscle);
    const muscleColorConfig = generateStaticMuscleColorConfig(workoutData.muscle_intensities);

    items.push({
      id: 'muscle_illustration',
      type: 'muscle_illustration',
      content: {
        muscle_data: {
          selectedMuscles,
          muscleColorConfig,
          intensities: workoutData.muscle_intensities
        }
      }
    });
  }

  // 添加用户图像项
  if (images && images.length > 0) {
    images.forEach((imageUrl, index) => {
      items.push({
        id: `user_image_${index}`,
        type: 'user_image',
        content: {
          image_data: {
            url: imageUrl,
            alt: `训练图片 ${index + 1}`,
            caption: index === 0 ? '训练现场' : undefined
          }
        }
      });
    });
  }

  return items;
}

/**
 * API帖子转换器
 */
export const feedPostTransformer = {
  fromApi(apiPost: ApiFeedPost): FeedPost {
    // 转换用户信息
    const user = userTransformer.fromApi(apiPost.user);

    // 转换训练数据
    let workoutData: WorkoutData | undefined;
    if (apiPost.related_workout_detail) {
      workoutData = workoutDetailTransformer.fromApi(apiPost.related_workout_detail);
    }

    // 生成轮播图项目
    const carouselItems = generateCarouselItems(workoutData, apiPost.images);

    return {
      id: apiPost.id.toString(),
      title: apiPost.title,
      user,
      content: {
        text: apiPost.content,
        workout: workoutData,
        images: apiPost.images,
        carousel_items: carouselItems
      },
      stats: {
        likes: apiPost.like_count,
        comments: apiPost.comment_count,
        shares: apiPost.share_count,
        views: apiPost.view_count,
        reports: apiPost.reported_count
      },
      isLiked: apiPost.is_liked_by_current_user,
      timestamp: new Date(apiPost.created_at),
      location: undefined, // API中暂无位置信息
      visibility: 'everyone', // 默认公开
      tags: apiPost.tags || [],
      status: apiPost.status
    };
  }
};

/**
 * 数据验证器
 */
export const dataValidator = {
  validateApiPost(apiPost: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查必需字段
    const requiredFields = ['id', 'content', 'user', 'created_at'];
    requiredFields.forEach(field => {
      if (!(field in apiPost) || apiPost[field] === null || apiPost[field] === undefined) {
        errors.push(`缺少必需字段: ${field}`);
      }
    });

    // 检查用户对象
    if (apiPost.user && typeof apiPost.user === 'object') {
      if (!apiPost.user.id || !apiPost.user.nickname) {
        errors.push('用户信息不完整');
      }
    }

    // 检查统计字段
    const statFields = ['like_count', 'comment_count', 'share_count'];
    statFields.forEach(field => {
      if (field in apiPost && typeof apiPost[field] !== 'number') {
        errors.push(`统计字段 ${field} 类型错误`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
};

export default {
  setRecordTransformer,
  workoutExerciseTransformer,
  userTransformer,
  workoutDetailTransformer,
  feedPostTransformer,
  dataValidator
};
