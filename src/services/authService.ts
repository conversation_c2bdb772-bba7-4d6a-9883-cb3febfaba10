/**
 * 用户认证服务
 * 基于memory-bank/api/auth.js的认证实现方式
 * 使用指定的测试用户信息进行认证
 */

// 测试用户配置
const TEST_USER_CONFIG = {
  user_id: 1,
  email: '<EMAIL>',
  openid: 'oCU0j7Rg9kzigLzquCBje3KfnQXk'
} as const;

// 认证相关接口定义
export interface AuthTokens {
  accessToken: string;
  refreshToken?: string;
  expiresAt: string;
}

export interface AuthUser {
  id: number;
  email: string;
  openid: string;
  nickName: string;
  avatarUrl: string;
  phone?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  created_at?: string;
}

export interface AuthConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  mockMode: boolean;
}

export interface LoginResponse {
  success: boolean;
  token: string;
  user: AuthUser;
  expires_at?: string;
  is_new_user?: boolean;
}

/**
 * 认证服务类
 */
export class AuthService {
  private config: AuthConfig;
  private tokens: AuthTokens | null = null;
  private user: AuthUser | null = null;
  private refreshPromise: Promise<void> | null = null;

  constructor(config?: Partial<AuthConfig>) {
    this.config = {
      baseUrl: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8000',
      timeout: 10000,
      retryAttempts: 3,
      mockMode: process.env.NODE_ENV === 'development',
      ...config
    };

    this.loadStoredAuth();
  }

  /**
   * 使用测试用户信息登录
   */
  async loginWithTestUser(): Promise<AuthUser> {
    console.log('【认证服务】开始测试用户登录');

    try {
      // 检查是否使用模拟模式
      if (this.config.mockMode) {
        console.log('【认证服务】开发环境，使用模拟数据');
        return this.mockLogin();
      }

      // 正式环境登录
      const response = await this.makeAuthRequest<LoginResponse>('/api/v1/auth/login/test', {
        method: 'POST',
        body: JSON.stringify(TEST_USER_CONFIG)
      });

      if (!response.success) {
        throw new Error('登录失败: ' + (response as any).message);
      }

      // 保存认证信息
      this.tokens = {
        accessToken: response.token,
        expiresAt: response.expires_at || this.getDefaultExpiry()
      };

      this.user = response.user;
      this.saveAuthToStorage();

      console.log('【认证服务】测试用户登录成功', this.user);
      return this.user;

    } catch (error) {
      console.error('【认证服务】测试用户登录失败:', error);
      
      // 登录失败时尝试使用模拟数据
      if (!this.config.mockMode) {
        console.log('【认证服务】登录失败，降级到模拟模式');
        return this.mockLogin();
      }
      
      throw error;
    }
  }

  /**
   * 模拟登录（开发环境或降级使用）
   */
  private mockLogin(): AuthUser {
    const mockUser: AuthUser = {
      id: TEST_USER_CONFIG.user_id,
      email: TEST_USER_CONFIG.email,
      openid: TEST_USER_CONFIG.openid,
      nickName: '测试用户',
      avatarUrl: '/api/placeholder/40/40',
      phone: '138****1234',
      gender: 1,
      country: '中国',
      province: '上海',
      city: '上海',
      created_at: new Date().toISOString()
    };

    this.tokens = {
      accessToken: 'mock_token_' + Date.now(),
      expiresAt: this.getDefaultExpiry()
    };

    this.user = mockUser;
    this.saveAuthToStorage();

    console.log('【认证服务】模拟登录成功', mockUser);
    return mockUser;
  }

  /**
   * 获取认证头信息
   */
  getAuthHeaders(): Record<string, string> {
    if (!this.tokens?.accessToken) {
      throw new Error('用户未登录，无法获取认证头');
    }

    return {
      'Authorization': `Bearer ${this.tokens.accessToken}`,
      'Content-Type': 'application/json',
      'X-User-ID': this.user?.id.toString() || '',
      'X-User-OpenID': this.user?.openid || ''
    };
  }

  /**
   * 检查token是否有效
   */
  isTokenValid(): boolean {
    if (!this.tokens) {
      console.log('【认证服务】无token信息');
      return false;
    }

    const now = new Date();
    const expiresAt = new Date(this.tokens.expiresAt);
    const isValid = now < expiresAt;

    console.log('【认证服务】Token有效性检查:', {
      current: now.toISOString(),
      expires: expiresAt.toISOString(),
      valid: isValid
    });

    return isValid;
  }

  /**
   * 自动刷新token（如果需要）
   */
  async refreshTokenIfNeeded(): Promise<void> {
    // 如果token仍然有效，无需刷新
    if (this.isTokenValid()) {
      return;
    }

    // 如果已经在刷新中，等待刷新完成
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    console.log('【认证服务】开始刷新token');

    this.refreshPromise = this.performTokenRefresh();
    
    try {
      await this.refreshPromise;
    } finally {
      this.refreshPromise = null;
    }
  }

  /**
   * 执行token刷新
   */
  private async performTokenRefresh(): Promise<void> {
    try {
      if (this.config.mockMode || !this.tokens) {
        // 模拟模式或无token时，重新登录
        await this.loginWithTestUser();
        return;
      }

      const response = await this.makeAuthRequest<{
        success: boolean;
        token: string;
        expires_at?: string;
      }>('/api/v1/auth/refresh', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.tokens.accessToken}`
        }
      });

      if (!response.success) {
        throw new Error('刷新token失败');
      }

      // 更新token信息
      this.tokens = {
        accessToken: response.token,
        expiresAt: response.expires_at || this.getDefaultExpiry()
      };

      this.saveAuthToStorage();
      console.log('【认证服务】Token刷新成功');

    } catch (error) {
      console.error('【认证服务】Token刷新失败:', error);
      
      // 刷新失败时清除认证信息并重新登录
      this.logout();
      await this.loginWithTestUser();
    }
  }

  /**
   * 获取当前用户信息
   */
  getCurrentUser(): AuthUser | null {
    return this.user;
  }

  /**
   * 检查是否已登录
   */
  isAuthenticated(): boolean {
    return this.user !== null && this.isTokenValid();
  }

  /**
   * 登出
   */
  logout(): void {
    console.log('【认证服务】用户登出');
    
    this.tokens = null;
    this.user = null;
    this.clearStoredAuth();
  }

  /**
   * 发起认证请求
   */
  private async makeAuthRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.config.baseUrl}${endpoint}`;

    // 创建 AbortController 来处理超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    const requestOptions: RequestInit = {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      signal: controller.signal
    };

    console.log('【认证服务】发起请求:', url, requestOptions);

    try {
      const response = await fetch(url, requestOptions);
      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`认证请求失败: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('【认证服务】请求响应:', data);

      return data;
    } catch (error) {
      clearTimeout(timeoutId);
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请检查网络连接');
      }
      throw error;
    }
  }

  /**
   * 从本地存储加载认证信息
   */
  private loadStoredAuth(): void {
    try {
      const storedTokens = localStorage.getItem('fitmaster_auth_tokens');
      const storedUser = localStorage.getItem('fitmaster_auth_user');

      if (storedTokens) {
        this.tokens = JSON.parse(storedTokens);
      }

      if (storedUser) {
        this.user = JSON.parse(storedUser);
      }

      console.log('【认证服务】加载存储的认证信息:', {
        hasTokens: !!this.tokens,
        hasUser: !!this.user
      });

    } catch (error) {
      console.error('【认证服务】加载存储认证信息失败:', error);
      this.clearStoredAuth();
    }
  }

  /**
   * 保存认证信息到本地存储
   */
  private saveAuthToStorage(): void {
    try {
      if (this.tokens) {
        localStorage.setItem('fitmaster_auth_tokens', JSON.stringify(this.tokens));
      }

      if (this.user) {
        localStorage.setItem('fitmaster_auth_user', JSON.stringify(this.user));
      }

      console.log('【认证服务】认证信息已保存到本地存储');

    } catch (error) {
      console.error('【认证服务】保存认证信息失败:', error);
    }
  }

  /**
   * 清除本地存储的认证信息
   */
  private clearStoredAuth(): void {
    localStorage.removeItem('fitmaster_auth_tokens');
    localStorage.removeItem('fitmaster_auth_user');
    console.log('【认证服务】已清除本地存储的认证信息');
  }

  /**
   * 获取默认过期时间（24小时后）
   */
  private getDefaultExpiry(): string {
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + 24);
    return expiry.toISOString();
  }
}

// 创建单例实例
export const authService = new AuthService();

// 导出默认实例
export default authService;
