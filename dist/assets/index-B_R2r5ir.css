@charset "UTF-8";.Button-module__pixelButton___8EYeN{background-color:var(--button-custom-bg,var(--bg-button,#f0f0f0));border-color:var(--button-custom-border,var(--border-button,#000));border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var(--button-custom-shadow,var(--shadow-button,#000)),-2px -2px 0 2px var(--button-custom-bg,var(--bg-button,#f0f0f0));color:var(--button-custom-text,var(--text-button,#000));display:inline-block;font-family:Minecraft,sans-serif;margin:.5rem .75rem;padding:.5rem;position:relative}.Button-module__pixelButton___8EYeN:active{box-shadow:2px 2px 0 2px var(--button-custom-bg,var(--bg-button,#f0f0f0)),-2px -2px 0 2px var(--button-custom-bg,var(--bg-button,#f0f0f0));transform:translateY(2px)}.Card-module__pixelCard___RY5ZX{background-color:var(--card-custom-bg,var(--bg-card,#fff));border-color:var(--card-custom-border,var(--border-card,#000));border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var(--card-custom-shadow,var(--shadow-card,#000)),-2px -2px 0 2px var(--card-custom-bg,var(--bg-card,#fff));color:var(--card-custom-text,var(--text-card,#000));font-family:Minecraft,sans-serif;font-size:1rem;line-height:1.5rem;margin:.5rem;padding:1px}.Dropdown-module__dropdownMenu___5R4vo{display:inline-block;font-family:Minecraft,sans-serif;font-size:1rem;line-height:1.5rem;position:relative}.Dropdown-module__pixelButton___JLKjb{background-color:var( --button-custom-bg,var(--dropdown-custom-bg,var(--bg-button,#f0f0f0)) );border-color:var( --button-custom-border,var(--dropdown-custom-border,var(--border-button,#000)) );border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var( --button-custom-shadow,var(--dropdown-custom-shadow,var(--shadow-button,#000)) ),-2px -2px 0 2px var( --button-custom-bg,var(--dropdown-custom-bg,var(--bg-button,#f0f0f0)) );color:var( --button-custom-text,var(--dropdown-custom-text,var(--text-button,#000)) );display:inline-block;font-family:Minecraft,sans-serif;position:relative}.Dropdown-module__pixelButton___JLKjb:active{box-shadow:2px 2px 0 2px var( --button-custom-bg,var(--dropdown-custom-bg,var(--bg-button,#f0f0f0)) ),-2px -2px 0 2px var( --button-custom-bg,var(--dropdown-custom-bg,var(--bg-button,#f0f0f0)) );transform:translateY(2px)}.Dropdown-module__dropdownMenuTrigger___6XWhP{align-items:center;display:flex;justify-content:space-between}.Dropdown-module__dropdownArrow___3Ow6J{height:1rem;margin-left:.5rem;mask-position:center;-webkit-mask-position:center;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;mask-size:contain;-webkit-mask-size:contain;transition-duration:.3s;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);width:1rem}.Dropdown-module__dropdownMenuContent___8jthO{background-color:var( --dropdown-content-custom-bg,var(--dropdown-custom-bg,var(--bg-dropdown,#fff)) );border-color:var( --dropdown-content-custom-border,var(--dropdown-custom-border,var(--border-dropdown,#000)) );border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var( --dropdown-content-custom-shadow,var(--dropdown-custom-shadow,var(--shadow-dropdown,#000)) ),-2px -2px 0 2px var( --dropdown-content-custom-bg,var(--dropdown-custom-bg,var(--bg-dropdown,#fff)) );color:var( --dropdown-content-custom-text,var(--dropdown-custom-text,var(--text-dropdown,#000)) );left:0;position:absolute;top:calc(100% + 16px);z-index:10}.Dropdown-module__dropdownMenuLabel___AU6WM{font-weight:700}.Dropdown-module__dropdownMenuItem___dj4Gp{cursor:pointer}.Dropdown-module__dropdownMenuItem___dj4Gp:hover{--tw-bg-opacity:1;background-color:rgb(243 244 246/var(--tw-bg-opacity));background-color:var( --dropdown-content-custom-bg,var(--dropdown-custom-bg,var(--bg-dropdown-hover,#e0e0e0)) )}.Dropdown-module__dropdownMenuSeparator___cgZY-{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity));height:1px;margin-bottom:.25rem;margin-top:.25rem}.ProgressBar-module__pixelProgressbarContainer___eQrfa{background-color:transparent;border-color:var( --progressbar-custom-border-color,var(--border-progressbar,#000) );border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;height:30px;padding:2px;position:relative;width:100%}.ProgressBar-module__pixelProgressbar___naQch{background-color:var( --progressbar-custom-color,var(--color-progressbar,#000) );height:100%;opacity:50%}.ProgressBar-module__pixelProgressbarSm___bcfOY{height:20px}.ProgressBar-module__pixelProgressbarMd___EBy8U{height:30px}.ProgressBar-module__pixelProgressbarLg___b2T9x{height:40px}.Popup-module__pixelPopupOverlay___sCrPr{align-items:center;background-color:var(--popup-overlay-bg,rgba(0,0,0,.5));display:flex;top:0;right:0;bottom:0;left:0;justify-content:center;position:fixed;z-index:50}.Popup-module__pixelPopup___n--zK{background-color:var(--popup-base-bg,var(--bg-popup-base,#fff));box-shadow:2px 2px 0 2px var(--popup-base-bg,var(--bg-popup-base,#fff)),-2px -2px 0 2px var(--popup-base-bg,var(--bg-popup-base,#fff));padding:.25rem;position:relative}.Popup-module__pixelPopupInner___8KoDw,.Popup-module__pixelPopup___n--zK{border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-source:var(--popup-border-svg);border-image-width:2;border-style:solid;border-width:5px;color:var(--popup-text,var(--text-popup,#000))}.Popup-module__pixelPopupInner___8KoDw{background-color:var(--popup-bg,var(--bg-popup,#f0f0f0));box-shadow:2px 2px 0 2px var(--popup-bg,var(--bg-popup,#f0f0f0)),-2px -2px 0 2px var(--popup-bg,var(--bg-popup,#f0f0f0));padding:1rem}.Popup-module__pixelPopupTitle___ofGZp{font-family:Minecraft,sans-serif;font-size:1.5rem;line-height:2rem;margin-bottom:1rem;text-align:center}.Popup-module__pixelPopupCloseButton___BiT6T{background-color:transparent;border-style:none;color:var(--popup-text,var(--text-popup,#000));cursor:pointer;font-family:Minecraft,sans-serif;font-size:1.125rem;line-height:1.75rem;position:absolute;right:.5rem;top:.25rem}.Popup-module__pixelPopupContent___ECzm3{font-family:Minecraft,sans-serif}.Input-module__pixelContainer___q-uvd{background-color:var(--input-custom-bg,var(--bg-input,#fff));border-color:var(--input-custom-border,var(--border-input,#000));border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var(--input-custom-bg,var(--bg-input,#fff)),-2px -2px 0 2px var(--input-custom-bg,var(--bg-input,#fff));color:var(--input-custom-text,var(--text-input,#000));display:inline-block;font-size:16px;position:relative}.Input-module__pixelInput___iCtVe{background-color:transparent;color:inherit;padding:.25rem .5rem}.Input-module__pixelInput___iCtVe:focus{outline:2px solid transparent;outline-offset:2px}.Input-module__pixelInputIconButton___RE0AJ{margin-right:.25rem;padding:.25rem}.Input-module__pixelInputIconButton___RE0AJ:active{top:2px}.TextArea-module__pixelTextarea___PfPoJ{background-color:var(--textarea-custom-bg,var(--bg-textarea,#f0f0f0));border-color:var(--textarea-custom-border,var(--border-textarea,#000));border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var(--textarea-custom-bg,var(--bg-textarea,#f0f0f0)),-2px -2px 0 2px var(--textarea-custom-bg,var(--bg-textarea,#f0f0f0));color:var(--textarea-custom-text,var(--text-textarea,#000));font-size:1rem;line-height:1.5rem;min-height:100px;padding:.5rem;resize:both;width:100%}.TextArea-module__pixelTextarea___PfPoJ,.TextArea-module__pixelTextarea___PfPoJ:focus{outline:2px solid transparent;outline-offset:2px}.Accordion-module__accordion___LVhhv{font-family:Minecraft,sans-serif;font-size:1rem;line-height:1.5rem;width:100%}.Accordion-module__accordionItem___bti-c{border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-width:2;border-style:solid;border-width:5px;box-shadow:2px 2px 0 2px var( --accordion-item-custom-shadow,var(--accordion-custom-shadow,var(--shadow-accordion,#000)) ),-2px -2px 0 2px var( --accordion-item-custom-bg,var(--accordion-custom-bg,var(--bg-accordion,#fff)) );margin-bottom:1.25rem;overflow:hidden}.Accordion-module__accordionItem___bti-c,.Accordion-module__accordionTrigger___0k6Bx{background-color:var( --accordion-item-custom-bg,var(--accordion-custom-bg,var(--bg-accordion,#fff)) );color:var( --accordion-item-custom-text,var(--accordion-custom-text,var(--text-accordion,#000)) )}.Accordion-module__accordionTrigger___0k6Bx{align-items:center;cursor:pointer;display:flex;gap:1rem;padding:.25rem 1rem;text-align:left;width:100%}.Accordion-module__accordionArrow___ex81W{height:1.5rem;mask-position:center;-webkit-mask-position:center;mask-repeat:no-repeat;-webkit-mask-repeat:no-repeat;mask-size:contain;-webkit-mask-size:contain;transition-duration:.3s;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1);width:1.5rem}.Accordion-module__accordionContent___Qnx7K{background-color:var( --accordion-item-custom-bg,var(--accordion-custom-bg,var(--bg-accordion,#fff)) );color:var( --accordion-item-custom-text,var(--accordion-custom-text,var(--text-accordion,#000)) );overflow:hidden;transition-duration:.3s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.Accordion-module__accordionContentInner___tDrUu{--tw-border-opacity:1;border-color:rgb(229 231 235/var(--tw-border-opacity));border-top-width:1px;font-size:.875rem;line-height:1.25rem;padding:1rem}.Bubble-module__balloon___23NCI{background-color:var(--bubble-bg-color,#fff);border-radius:4px;color:var(--bubble-text-color,#000);cursor:pointer;display:inline-block;margin:8px 8px 30px;padding:1rem 1.5rem;position:relative}.Bubble-module__balloon___23NCI>:last-child{margin-bottom:0}.Bubble-module__balloon___23NCI:after,.Bubble-module__balloon___23NCI:before{content:"";position:absolute}.Bubble-module__balloon___23NCI.Bubble-module__from-left___7AxNu:after,.Bubble-module__balloon___23NCI.Bubble-module__from-left___7AxNu:before{left:2rem}.Bubble-module__balloon___23NCI.Bubble-module__from-left___7AxNu:before{background-color:var(--bubble-bg-color,#fff);border-left:4px solid var(--bubble-border-color,#000);border-right:4px solid var(--bubble-border-color,#000);bottom:-14px;height:10px;width:26px}.Bubble-module__balloon___23NCI.Bubble-module__from-left___7AxNu:after{background-color:var(--bubble-bg-color,#fff);bottom:-18px;box-shadow:-4px 0 var(--bubble-border-color,#000),4px 0 var(--bubble-border-color,#000),-4px 4px var(--bubble-bg-color,#fff),0 4px var(--bubble-border-color,#000),-8px 4px var(--bubble-border-color,#000),-4px 8px var(--bubble-border-color,#000),-8px 8px var(--bubble-border-color,#000);height:4px;margin-right:8px;width:18px}.Bubble-module__balloon___23NCI.Bubble-module__from-right___oryys:after,.Bubble-module__balloon___23NCI.Bubble-module__from-right___oryys:before{right:2rem}.Bubble-module__balloon___23NCI.Bubble-module__from-right___oryys:before{background-color:var(--bubble-bg-color,#fff);border-left:4px solid var(--bubble-border-color,#000);border-right:4px solid var(--bubble-border-color,#000);bottom:-14px;height:10px;width:26px}.Bubble-module__balloon___23NCI.Bubble-module__from-right___oryys:after{background-color:var(--bubble-bg-color,#fff);bottom:-18px;box-shadow:-4px 0 var(--bubble-border-color,#000),4px 0 var(--bubble-border-color,#000),4px 4px var(--bubble-bg-color,#fff),0 4px var(--bubble-border-color,#000),8px 4px var(--bubble-border-color,#000),4px 8px var(--bubble-border-color,#000),8px 8px var(--bubble-border-color,#000);height:4px;margin-left:8px;width:18px}.Bubble-module__roundedCorners___TPJks{border-image-outset:2;border-image-repeat:stretch;border-image-slice:3;border-image-source:var(--bubble-border-image);border-image-width:3;border-style:solid;border-width:4px}*,:after,:before{border:0 solid #e5e7eb;box-sizing:border-box}:after,:before{--tw-content:""}:host,html{-webkit-text-size-adjust:100%;font-feature-settings:normal;-webkit-tap-highlight-color:transparent;font-family:ui-sans-serif,system-ui,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-variation-settings:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4}body{line-height:inherit;margin:0}hr{border-top-width:1px;color:inherit;height:0}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,pre,samp{font-feature-settings:normal;font-family:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,Liberation Mono,Courier New,monospace;font-size:1em;font-variation-settings:normal}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}table{border-collapse:collapse;border-color:inherit;text-indent:0}button,input,optgroup,select,textarea{font-feature-settings:inherit;color:inherit;font-family:inherit;font-size:100%;font-variation-settings:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;margin:0;padding:0}button,select{text-transform:none}button,input:where([type=button]),input:where([type=reset]),input:where([type=submit]){-webkit-appearance:button;background-color:transparent;background-image:none}:-moz-focusring{outline:auto}:-moz-ui-invalid{box-shadow:none}progress{vertical-align:baseline}::-webkit-inner-spin-button,::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}blockquote,dd,dl,figure,h1,h2,h3,h4,h5,h6,hr,p,pre{margin:0}fieldset{margin:0}fieldset,legend{padding:0}menu,ol,ul{list-style:none;margin:0;padding:0}dialog{padding:0}textarea{resize:vertical}input::-moz-placeholder,textarea::-moz-placeholder{color:#9ca3af;opacity:1}input::placeholder,textarea::placeholder{color:#9ca3af;opacity:1}[role=button],button{cursor:pointer}:disabled{cursor:default}audio,canvas,embed,iframe,img,object,svg,video{display:block;vertical-align:middle}img,video{height:auto;max-width:100%}[hidden]{display:none}*,:after,:before{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }::backdrop{--tw-border-spacing-x:0;--tw-border-spacing-y:0;--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1;--tw-pan-x: ;--tw-pan-y: ;--tw-pinch-zoom: ;--tw-scroll-snap-strictness:proximity;--tw-gradient-from-position: ;--tw-gradient-via-position: ;--tw-gradient-to-position: ;--tw-ordinal: ;--tw-slashed-zero: ;--tw-numeric-figure: ;--tw-numeric-spacing: ;--tw-numeric-fraction: ;--tw-ring-inset: ;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-color:rgba(59,130,246,.5);--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;--tw-blur: ;--tw-brightness: ;--tw-contrast: ;--tw-grayscale: ;--tw-hue-rotate: ;--tw-invert: ;--tw-saturate: ;--tw-sepia: ;--tw-drop-shadow: ;--tw-backdrop-blur: ;--tw-backdrop-brightness: ;--tw-backdrop-contrast: ;--tw-backdrop-grayscale: ;--tw-backdrop-hue-rotate: ;--tw-backdrop-invert: ;--tw-backdrop-opacity: ;--tw-backdrop-saturate: ;--tw-backdrop-sepia: ;--tw-contain-size: ;--tw-contain-layout: ;--tw-contain-paint: ;--tw-contain-style: }.container{width:100%}@media (min-width:640px){.container{max-width:640px}}@media (min-width:768px){.container{max-width:768px}}@media (min-width:1024px){.container{max-width:1024px}}@media (min-width:1280px){.container{max-width:1280px}}@media (min-width:1536px){.container{max-width:1536px}}.visible{visibility:visible}.fixed{position:fixed}.absolute{position:absolute}.relative{position:relative}.inset-0{top:0;right:0;bottom:0;left:0}.right-0{right:0}.right-2{right:.5rem}.top-0{top:0}.top-1{top:.25rem}.top-\[2px\]{top:2px}.z-10{z-index:10}.m-2{margin:.5rem}.mx-1{margin-left:.25rem;margin-right:.25rem}.mx-3{margin-left:.75rem;margin-right:.75rem}.my-2{margin-bottom:.5rem;margin-top:.5rem}.mb-4{margin-bottom:1rem}.mb-5{margin-bottom:1.25rem}.block{display:block}.inline-block{display:inline-block}.h-1{height:.25rem}.h-4{height:1rem}.h-5{height:1.25rem}.h-6{height:1.5rem}.h-px{height:1px}.min-h-\[100px\]{min-height:100px}.w-4{width:1rem}.w-5{width:1.25rem}.w-6{width:1.5rem}.w-full{width:100%}.transform{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skew(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.cursor-pointer{cursor:pointer}.resize{resize:both}.gap-4{gap:1rem}.border{border-width:1px}.border-\[5px\]{border-width:5px}.border-t{border-top-width:1px}.border-solid{border-style:solid}.border-none{border-style:none}.bg-gray-200{--tw-bg-opacity:1;background-color:rgb(229 231 235/var(--tw-bg-opacity))}.bg-transparent{background-color:transparent}.\!p-0{padding:0!important}.p-0{padding:0}.p-1{padding:.25rem}.p-2{padding:.5rem}.p-4{padding:1rem}.px-2{padding-left:.5rem;padding-right:.5rem}.px-4{padding-left:1rem;padding-right:1rem}.py-1{padding-bottom:.25rem;padding-top:.25rem}.py-2{padding-bottom:.5rem;padding-top:.5rem}.pr-7{padding-right:1.75rem}.font-minecraft{font-family:Minecraft,sans-serif}.text-2xl{font-size:1.5rem;line-height:2rem}.text-base{font-size:1rem;line-height:1.5rem}.text-lg{font-size:1.125rem;line-height:1.75rem}.text-sm{font-size:.875rem;line-height:1.25rem}.shadow{--tw-shadow:0 1px 3px 0 rgba(0,0,0,.1),0 1px 2px -1px rgba(0,0,0,.1);--tw-shadow-colored:0 1px 3px 0 var(--tw-shadow-color),0 1px 2px -1px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.outline{outline-style:solid}.filter{filter:var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)}.transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.transition-transform{transition-duration:.15s;transition-property:transform;transition-timing-function:cubic-bezier(.4,0,.2,1)}.duration-300{transition-duration:.3s}.ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}:root{--primary-bg:var(--primary-bg-color,#c381b5);--primary-text:var(--primary-text-color,#fefcd0);--primary-shadow:var(--primary-box-shadow,#fefcd0);--secondary-bg:var(--secondary-bg-color,#fefcd0);--secondary-text:var(--secondary-text-color,#000);--secondary-shadow:var(--secondary-box-shadow,#c381b5);--outline-text:var(--outline-text-color,#000);--primary-bg-dropdown:#fff;--outline-text:#000;--bg-textarea:#f0f0f0;--text-textarea:#000;--border-textarea-rgb:0,0,0;--color-progressbar:#000;--border-progressbar:#000;--bg-input:#fff;--text-input:#000;--border-input:#000;--bg-card:#fff;--text-card:#000;--border-card:#000;--shadow-card:#767676;--bg-dropdown:transparent;--text-dropdown:#000;--border-dropdown:#000;--bg-popup-base:#fff;--bg-popup:#f0f0f0;--text-popup:#000}body{font-family:Minecraft,sans-serif}.hn{display:inline-flex;align-items:center;justify-content:center}.hn.icon-small{font-size:14px;width:16px;height:16px;line-height:16px}.hn.icon-medium{font-size:18px;width:20px;height:20px;line-height:20px}.hn.icon-large{font-size:24px;width:28px;height:28px;line-height:28px}.hn.icon-xlarge{font-size:32px;width:36px;height:36px;line-height:36px}.sidebar{width:var(--sidebar-width);min-height:100vh;background:var(--primary-700);border-right:1px solid var(--primary-500);display:flex;flex-direction:column;position:fixed;left:0;top:0;z-index:var(--z-fixed);transition:all var(--transition-normal)}.sidebar-header{padding:var(--space-6) var(--space-4);border-bottom:1px solid var(--primary-500)}.brand{display:flex;align-items:center;gap:var(--space-3);text-decoration:none;color:var(--text-primary)}.brand-icon{width:2rem;height:2rem;display:flex;align-items:center;justify-content:center;background:var(--accent-500);border-radius:var(--radius-lg);color:var(--text-on-accent)}.brand-icon svg{width:1.25rem;height:1.25rem;stroke-width:2}.brand-text{font-size:var(--text-xl);font-weight:var(--font-bold);font-family:var(--font-display)}.sidebar-nav{flex:1;padding:var(--space-4) 0;overflow-y:auto}.nav-list{list-style:none;margin:0;padding:0}.nav-list-item{margin-bottom:var(--space-1)}.nav-item{display:flex;align-items:center;gap:var(--space-3);padding:var(--space-3) var(--space-4);margin:0 var(--space-2);color:var(--text-secondary);text-decoration:none;border-radius:var(--radius-lg);transition:all var(--transition-normal);position:relative;font-weight:var(--font-medium)}.nav-item .hn{width:20px;height:20px;font-size:20px;color:inherit;transition:all var(--transition-normal)}.nav-item .nav-label{font-size:var(--text-sm);font-weight:var(--font-medium);color:inherit}.nav-item .nav-badge{margin-left:auto;background:var(--error-500);color:var(--text-on-error);font-size:var(--text-xs);font-weight:var(--font-semibold);padding:.125rem .375rem;border-radius:var(--radius-full);line-height:1;min-width:1.125rem;text-align:center}.nav-item:hover{background:var(--primary-600);color:var(--text-primary);transform:translate(4px)}.nav-item.active{background:var(--accent-500);color:var(--text-on-accent)}.nav-item.active .hn{color:var(--text-on-accent)}.nav-item.active:before{content:"";position:absolute;left:-var(--space-2);top:0;bottom:0;width:3px;background:var(--text-on-accent);border-radius:0 var(--radius-sm) var(--radius-sm) 0}.nav-item-icon{display:flex;align-items:center;justify-content:center;width:1.5rem;height:1.5rem}.nav-item-icon .nav-icon{width:100%;height:100%;stroke-width:2}.nav-item-label{flex:1;font-size:var(--text-sm)}.nav-item-badge{background:var(--error-500);color:var(--text-on-error);font-size:var(--text-xs);font-weight:var(--font-semibold);padding:.125rem .375rem;border-radius:var(--radius-full);min-width:1.125rem;height:1.125rem;display:flex;align-items:center;justify-content:center;line-height:1}.sidebar-user{padding:var(--space-4);border-top:1px solid var(--primary-500);margin-top:auto}.user-profile{display:flex;align-items:center;gap:var(--space-3);padding:var(--space-3);border-radius:var(--radius-lg);background:var(--primary-600);transition:all var(--transition-normal);cursor:pointer}.user-profile:hover{background:var(--primary-500);transform:translateY(-1px)}.user-avatar{width:2.5rem;height:2.5rem;border-radius:var(--radius-full);overflow:hidden;border:2px solid var(--accent-500)}.user-avatar .avatar-image{width:100%;height:100%;object-fit:cover;display:block}.user-info{flex:1;min-width:0}.user-name{font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-primary);margin-bottom:.125rem;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.user-status{font-size:var(--text-xs);color:var(--success-400);white-space:nowrap;overflow:hidden;text-overflow:ellipsis}@media (max-width: 768px){.sidebar{transform:translate(-100%)}.sidebar.mobile-open{transform:translate(0)}}.sidebar-nav::-webkit-scrollbar{width:4px}.sidebar-nav::-webkit-scrollbar-track{background:var(--primary-700)}.sidebar-nav::-webkit-scrollbar-thumb{background:var(--primary-500);border-radius:var(--radius-full)}.sidebar-nav::-webkit-scrollbar-thumb:hover{background:var(--primary-400)}@media (min-width: 769px) and (max-width: 1024px){.sidebar{width:14rem}.brand-text{font-size:var(--text-lg, 1.125rem)}.nav-item{padding:var(--space-2, .5rem) var(--space-3, .75rem);font-size:var(--text-sm, .875rem)}}.sidebar-transition-enter{transform:translate(-100%)}.sidebar-transition-enter-active{transform:translate(0);transition:transform .3s ease-in-out}.sidebar-transition-exit{transform:translate(0)}.sidebar-transition-exit-active{transform:translate(-100%);transition:transform .3s ease-in-out}.nav-item:focus{outline:2px solid var(--accent-500, #3b82f6);outline-offset:2px}.user-profile:focus{outline:2px solid var(--accent-500, #3b82f6);outline-offset:2px}@media (prefers-contrast: high){.sidebar{border-right-width:2px}.nav-item{border:1px solid transparent}.nav-item:hover,.nav-item.active{border-color:currentColor}}@media (prefers-reduced-motion: reduce){.nav-item,.user-profile{transition:none}.nav-item:hover{transform:none}}.bottom-navigation{position:fixed!important;bottom:0!important;left:0!important;right:0!important;height:70px!important;background:var(--bg-surface)!important;border-top:1px solid var(--primary-500, rgba(71, 85, 105, .3))!important;z-index:9999!important;-webkit-backdrop-filter:blur(10px)!important;backdrop-filter:blur(10px)!important;box-shadow:0 -2px 10px #0000001a!important;display:block!important;width:100vw!important;padding-bottom:env(safe-area-inset-bottom)!important}.bottom-nav-container{display:flex!important;height:50px!important;align-items:center!important;justify-content:space-around!important;padding:0 var(--space-2, .5rem)!important;position:relative!important;margin-top:5px!important}.bottom-nav-item{display:flex!important;flex-direction:column!important;align-items:center!important;justify-content:center!important;padding:var(--space-2, .5rem)!important;background:none!important;border:none!important;color:var(--text-secondary, #cbd5e1)!important;cursor:pointer!important;border-radius:var(--radius-md, 6px)!important;transition:all .2s ease!important;position:relative!important;min-width:60px!important;gap:.25rem!important;flex:1!important;max-width:80px!important}.bottom-nav-item:hover:not(.active){color:var(--text-primary, #f8fafc)!important}.bottom-nav-item.active{color:var(--accent-500, #3b82f6)!important}.bottom-nav-item.active .bottom-nav-icon .hn{transform:scale(1.1)!important;color:var(--accent-500, #3b82f6)!important}.bottom-nav-icon{display:flex!important;align-items:center!important;justify-content:center!important;margin-bottom:4px!important}.bottom-nav-icon .hn{transition:all var(--transition-normal)!important;font-size:18px!important;width:22px!important;height:22px!important;display:flex!important;align-items:center!important;justify-content:center!important}.bottom-nav-label{font-size:.75rem!important;font-weight:500!important;line-height:1!important;text-align:center!important;white-space:nowrap!important;overflow:hidden!important;text-overflow:ellipsis!important;max-width:100%!important}.bottom-nav-badge{position:absolute!important;top:.25rem!important;right:.25rem!important;background:var(--error-500, #ef4444)!important;color:#fff!important;font-size:.625rem!important;font-weight:600!important;padding:0!important;min-width:16px!important;height:16px!important;border-radius:8px!important;display:flex!important;align-items:center!important;justify-content:center!important}.theme-dark .bottom-navigation{background:var(--bg-surface, #1e293b)!important;border-top-color:var(--border-color, #374151)!important}.theme-light .bottom-navigation{background:var(--bg-surface, #ffffff)!important;border-top-color:var(--border-color, #e5e7eb)!important}@media (max-width: 768px){.bottom-navigation{display:block!important;opacity:1!important;visibility:visible!important;transform:translateY(0)!important;position:fixed!important;z-index:10000!important}.bottom-navigation{height:calc(60px + env(safe-area-inset-bottom,0px))!important}}@media (max-width: 768px) and (orientation: landscape){.bottom-navigation{height:calc(50px + env(safe-area-inset-bottom,0px))!important}.bottom-nav-container{height:42px!important;margin-top:4px!important}.bottom-nav-item{padding:var(--space-1, .25rem)!important;min-height:38px!important}.bottom-nav-label{font-size:.7rem!important}}@media (max-width: 480px){.bottom-nav-item{min-width:50px!important;padding:var(--space-1, .25rem)!important;min-height:40px!important}.bottom-nav-item .bottom-nav-icon .hn{font-size:16px!important;width:20px!important;height:20px!important}.bottom-nav-label{font-size:.65rem!important}}@media (prefers-contrast: high){.bottom-navigation{border-top-width:2px!important}.bottom-nav-item{border:1px solid transparent!important}.bottom-nav-item:hover,.bottom-nav-item.active{border-color:currentColor!important}}@media (prefers-reduced-motion: reduce){.bottom-nav-item,.bottom-nav-item .bottom-nav-icon .hn{transition:none!important}}.debug-bottom-nav .bottom-navigation{background:#ff000080!important;border:3px solid red!important}.header-actions{display:flex;align-items:center;gap:12px;flex-shrink:0}.header-action-btn{display:flex;align-items:center;justify-content:center;width:44px;height:44px;border-radius:12px;background:var(--bg-surface);border:1px solid var(--border-color);color:var(--text-secondary);cursor:pointer;transition:all var(--transition-normal);position:relative}.header-action-btn .hn{width:20px;height:20px;font-size:20px;color:inherit}.header-action-btn:hover{background:var(--bg-hover);border-color:var(--primary-500);color:var(--text-primary);transform:translateY(-1px);box-shadow:0 4px 12px #4f46e526}.header-action-btn:active{transform:translateY(0);box-shadow:none}.header-action-btn:focus{outline:2px solid var(--accent-500);outline-offset:2px}.theme-btn:hover{background:var(--primary-600);border-color:var(--accent-500);color:var(--accent-400)}.theme-btn:hover .hn{color:var(--accent-400)}.notification-btn:hover{background:linear-gradient(135deg,#fef3c7,#fbbf24);border-color:#f59e0b;color:#f59e0b}.notification-btn:hover .hn{color:#f59e0b}.settings-btn:hover{background:linear-gradient(135deg,#e0e7ff,#8b5cf6);border-color:#8b5cf6;color:#8b5cf6}.settings-btn:hover .hn{color:#8b5cf6}@media (max-width: 768px){.header-actions{gap:8px}.header-action-btn{width:40px;height:40px;border-radius:10px}.header-action-btn .hn{width:18px;height:18px;font-size:18px}}@media (max-width: 480px){.header-actions{gap:6px}.header-action-btn{width:36px;height:36px}.header-action-btn .hn{width:16px;height:16px;font-size:16px}}@media (prefers-contrast: high){.header-action-btn{border-width:2px}.header-action-btn:hover,.header-action-btn:focus{border-width:2px}}@media (prefers-reduced-motion: reduce){.header-action-btn{transition:none}.header-action-btn:hover{transform:none}}.layout{display:flex;min-height:100vh;background:var(--bg-primary);color:var(--text-primary, #f8fafc);font-family:var(--font-primary, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif)}.main-content{flex:1;display:flex;flex-direction:column;transition:all var(--transition-normal);min-height:100vh}.main-content.with-sidebar{margin-left:var(--sidebar-width)}.main-content.full-width{margin-left:0}.page-header{background:var(--bg-secondary);border-bottom:1px solid var(--primary-500);padding:0 var(--space-6);height:var(--header-height);display:flex;align-items:center;position:sticky;top:0;z-index:var(--z-sticky);background:var(--bg-primary);border-bottom:1px solid var(--border-color);padding:16px 20px}.page-header .dashboard-header-content{display:flex;align-items:center;justify-content:space-between;width:100%}.page-header .dashboard-date-info{flex:1;min-width:0}.page-header .dashboard-title{font-size:18px;color:var(--text-primary);margin:0 0 4px;font-weight:700;line-height:1.2}.page-header .dashboard-subtitle{font-size:14px;color:var(--text-secondary);margin:0;line-height:1.2}.page-header .header-actions{flex-shrink:0}.page-header-content{display:flex;align-items:center;justify-content:space-between;width:100%;max-width:var(--container-7xl);margin:0 auto}.page-title{font-size:var(--text-2xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.page-actions{display:flex;align-items:center;gap:12px}.notification-badge{position:absolute;top:-2px;right:-2px;background:var(--error-500);color:var(--text-on-error);font-size:var(--text-xs);font-weight:var(--font-semibold);padding:.125rem .375rem;border-radius:var(--radius-full);line-height:1;min-width:1.125rem;text-align:center}.page-content{flex:1;padding:var(--space-6);overflow-y:auto}.page-content.no-header{padding-top:0}.page-content::-webkit-scrollbar{width:8px}.page-content::-webkit-scrollbar-track{background:var(--primary-600, #1e293b)}.page-content::-webkit-scrollbar-thumb{background:var(--primary-500, #334155);border-radius:4px}.page-content::-webkit-scrollbar-thumb:hover{background:var(--accent-500, #3b82f6)}.page-loading{display:flex;align-items:center;justify-content:center;min-height:50vh;flex-direction:column;gap:var(--space-4, 1rem)}.page-loading .loading-spinner{width:2rem;height:2rem;border:2px solid var(--primary-500, #334155);border-top-color:var(--accent-500, #3b82f6);border-radius:50%;animation:spin 1s linear infinite}.page-loading .loading-text{color:var(--text-secondary, #cbd5e1);font-size:var(--text-base, 1rem)}@keyframes spin{to{transform:rotate(360deg)}}.page-error{display:flex;align-items:center;justify-content:center;min-height:50vh;flex-direction:column;gap:var(--space-4, 1rem);text-align:center}.page-error .error-icon{width:3rem;height:3rem;color:var(--error-500, #ef4444)}.page-error .error-title{font-size:var(--text-xl, 1.25rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);margin:0}.page-error .error-message{color:var(--text-secondary, #cbd5e1);font-size:var(--text-base, 1rem);margin:0}.page-error .error-actions{display:flex;gap:var(--space-3, .75rem);margin-top:var(--space-4, 1rem)}@media (max-width: 768px){.page-header{padding:0 var(--space-4);height:auto;min-height:var(--header-height-mobile);padding:16px}.page-header .dashboard-header-content{flex-direction:row;gap:12px;align-items:center;justify-content:space-between;width:100%;min-height:48px}.page-header .dashboard-date-info{flex:1;min-width:0;display:block}.page-header .dashboard-date-info .dashboard-title{font-size:16px;margin:0 0 2px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.page-header .dashboard-date-info .dashboard-subtitle{font-size:12px;margin:0;color:var(--text-secondary)}.page-header .header-actions{flex-shrink:0}.page-content{padding:12px}}@media (max-width: 480px){.page-header{padding:8px 12px}.page-content{padding:12px}}@media (min-width: 769px) and (max-width: 1024px){.main-content.with-sidebar{margin-left:14rem}.page-header{padding:0 var(--space-5, 1.25rem)}.page-content{padding:var(--space-5, 1.25rem)}}@media (min-width: 1440px){.page-content{max-width:1200px;margin:0 auto;padding:var(--space-8, 2rem)}}@media (prefers-contrast: high){.page-header{border-bottom-width:2px}}@media (prefers-reduced-motion: reduce){.loading-spinner{animation:none}}@media print{.page-header,.page-actions{display:none}.main-content{margin-left:0}.page-content{padding:0}}.btn{position:relative;display:inline-flex;align-items:center;justify-content:center;gap:.5rem;font-family:inherit;font-weight:600;text-decoration:none;white-space:nowrap;border:none;border-radius:8px;cursor:pointer;-webkit-user-select:none;user-select:none;transition:all .2s cubic-bezier(.4,0,.2,1);transform:translateY(0)}.btn:focus{outline:none;box-shadow:0 0 0 3px #4fc3f74d}.btn:active{transform:translateY(1px)}.btn--sm{padding:.5rem 1rem;font-size:.875rem;min-height:32px}.btn--sm .btn__icon{width:16px;height:16px}.btn--md{padding:.75rem 1.5rem;font-size:1rem;min-height:40px}.btn--md .btn__icon{width:18px;height:18px}.btn--lg{padding:1rem 2rem;font-size:1.125rem;min-height:48px}.btn--lg .btn__icon{width:20px;height:20px}.btn--round{border-radius:9999px}.btn--circle{aspect-ratio:1;border-radius:50%;padding:0}.btn--circle.btn--sm{width:32px;height:32px}.btn--circle.btn--md{width:40px;height:40px}.btn--circle.btn--lg{width:48px;height:48px}.btn--full-width{width:100%}.btn--primary{background:linear-gradient(135deg,#4fc3f7,#29b6f6);color:#fff;box-shadow:0 2px 8px #4fc3f74d}.btn--primary:hover:not(:disabled){background:linear-gradient(135deg,#29b6f6,#0288d1);box-shadow:0 4px 16px #4fc3f766;transform:translateY(-2px)}.btn--primary:active{background:linear-gradient(135deg,#0288d1,#0277bd);transform:translateY(0)}.btn--secondary{background:var(--color-background-secondary);color:var(--color-text-primary);border:1px solid rgba(79,195,247,.3);box-shadow:0 2px 4px #0000001a}.btn--secondary:hover:not(:disabled){background:#4fc3f71a;border-color:var(--color-primary);box-shadow:0 4px 12px #00000026;transform:translateY(-1px)}.btn--secondary:active{background:#4fc3f733;transform:translateY(0)}.btn--ghost{background:transparent;color:var(--color-primary);border:1px solid transparent}.btn--ghost:hover:not(:disabled){background:#4fc3f71a;border-color:#4fc3f74d}.btn--ghost:active{background:#4fc3f733}.btn--danger{background:linear-gradient(135deg,#f44336,#d32f2f);color:#fff;box-shadow:0 2px 8px #f443364d}.btn--danger:hover:not(:disabled){background:linear-gradient(135deg,#d32f2f,#c62828);box-shadow:0 4px 16px #f4433666;transform:translateY(-2px)}.btn--danger:active{background:linear-gradient(135deg,#c62828,#b71c1c);transform:translateY(0)}.btn--success{background:linear-gradient(135deg,#66bb6a,#4caf50);color:#fff;box-shadow:0 2px 8px #4caf504d}.btn--success:hover:not(:disabled){background:linear-gradient(135deg,#4caf50,#388e3c);box-shadow:0 4px 16px #4caf5066;transform:translateY(-2px)}.btn--success:active{background:linear-gradient(135deg,#388e3c,#2e7d32);transform:translateY(0)}.btn--disabled,.btn:disabled{opacity:.5;cursor:not-allowed;transform:none!important;box-shadow:none!important}.btn--disabled:hover,.btn:disabled:hover{transform:none!important;box-shadow:none!important}.btn--loading{cursor:wait}.btn--loading .btn__text{opacity:.7}.btn .btn__icon{display:flex;align-items:center;justify-content:center;flex-shrink:0}.btn .btn__icon svg{width:100%;height:100%;stroke-width:2}.btn .btn__icon--left{margin-right:-.25rem}.btn .btn__icon--right{margin-left:-.25rem}.btn .btn__spinner{width:16px;height:16px;animation:spin 1s linear infinite}.btn .btn__spinner path:last-child{opacity:.3}.btn .btn__text{flex:1;text-align:center}@media (max-width: 767px){.btn--sm{padding:.5rem .75rem;font-size:.8rem;min-height:28px}.btn--md{padding:.75rem 1.25rem;font-size:.9rem;min-height:36px}.btn--lg{padding:1rem 1.75rem;font-size:1rem;min-height:44px}}@media (prefers-contrast: high){.btn{border-width:2px}.btn--primary{border:2px solid #0288d1}.btn--secondary{border-width:2px}.btn--ghost{border:2px solid var(--color-primary)}}@media (prefers-reduced-motion: reduce){.btn{transition:none}.btn:hover,.btn:active{transform:none}.btn__spinner{animation:none}}.card{display:flex;flex-direction:column;position:relative;transition:all .2s cubic-bezier(.4,0,.2,1)}.card--default{background:var(--color-background-secondary);border:1px solid rgba(79,195,247,.1)}.card--outlined{background:transparent;border:1px solid rgba(79,195,247,.3)}.card--elevated{background:var(--color-background-secondary);border:1px solid rgba(79,195,247,.1);box-shadow:0 4px 16px #0000001a}.card--ghost{background:#4fc3f70d;border:1px solid transparent}.card--sm{font-size:.875rem}.card--md{font-size:1rem}.card--lg{font-size:1.125rem}.card--radius-sm{border-radius:4px}.card--radius-md{border-radius:8px}.card--radius-lg{border-radius:12px}.card--radius-xl{border-radius:16px}.card--padding-none{padding:0}.card--padding-sm{padding:.75rem}.card--padding-md{padding:1rem}.card--padding-lg{padding:1.5rem}.card--full-width{width:100%}.card--clickable{cursor:pointer;-webkit-user-select:none;user-select:none}.card--clickable:hover{transform:translateY(-2px);box-shadow:0 8px 24px #00000026}.card--clickable:active{transform:translateY(0)}.card--hoverable:hover{transform:translateY(-1px);box-shadow:0 4px 16px #0000001a;border-color:var(--color-primary)}.card__header{display:flex;align-items:flex-start;justify-content:space-between;margin-bottom:1rem}.card__header:last-child{margin-bottom:0}.card__header-content{flex:1;min-width:0}.card__title{color:var(--color-text-primary);font-size:1.25rem;font-weight:600;margin:0 0 .25rem;line-height:1.4}.card__subtitle{color:var(--color-text-secondary);font-size:.875rem;margin:0;line-height:1.4;opacity:.8}.card__action{margin-left:1rem;flex-shrink:0}.card__content{flex:1;color:var(--color-text-primary);line-height:1.6}.card__content:not(:last-child){margin-bottom:1rem}.card__content p{margin:0 0 .75rem}.card__content p:last-child{margin-bottom:0}.card__content ul,.card__content ol{margin:0 0 .75rem;padding-left:1.5rem}.card__content ul:last-child,.card__content ol:last-child{margin-bottom:0}.card__content li{margin-bottom:.25rem}.card__content li:last-child{margin-bottom:0}.card__footer{margin-top:auto;color:var(--color-text-secondary);font-size:.875rem}.card__footer--divider{padding-top:1rem;border-top:1px solid rgba(79,195,247,.1)}.card__footer .btn+.btn{margin-left:.5rem}.card--padding-none .card__header,.card--padding-none .card__content,.card--padding-none .card__footer{padding:1rem}.card--padding-none .card__footer--divider{padding-top:1rem;margin-top:1rem}@media (min-width: 768px) and (max-width: 1023px){.card--padding-lg{padding:1.25rem}.card__title{font-size:1.125rem}}@media (max-width: 767px){.card--padding-sm{padding:.5rem}.card--padding-md{padding:.75rem}.card--padding-lg{padding:1rem}.card__title{font-size:1rem}.card__subtitle{font-size:.8rem}.card__action{margin-left:.5rem}.card__header{margin-bottom:.75rem}.card__content:not(:last-child){margin-bottom:.75rem}.card__footer--divider{padding-top:.75rem;margin-top:.75rem}}@media (prefers-color-scheme: dark){.card--elevated{box-shadow:0 4px 16px #0000004d}.card--clickable:hover{box-shadow:0 8px 24px #0006}.card--hoverable:hover{box-shadow:0 4px 16px #0000004d}}@media (prefers-contrast: high){.card,.card--outlined{border-width:2px}.card__footer--divider{border-top-width:2px}}@media (prefers-reduced-motion: reduce){.card{transition:none}.card--clickable:hover,.card--hoverable:hover,.card--clickable:active{transform:none}}.input-field{display:flex;flex-direction:column;gap:.5rem}.input-field--full-width{width:100%}.input-label{color:var(--color-text-primary);font-size:.875rem;font-weight:500;line-height:1.4}.input-required{color:var(--error-500);margin-left:.25rem}.input-wrapper{position:relative;display:flex;align-items:center}.input{width:100%;border:1px solid rgba(79,195,247,.3);border-radius:8px;background:var(--color-background-secondary);color:var(--color-text-primary);font-family:inherit;font-size:1rem;line-height:1.5;transition:all .2s cubic-bezier(.4,0,.2,1)}.input::placeholder{color:var(--color-text-secondary);opacity:.6}.input:focus{outline:none;border-color:var(--color-primary);box-shadow:0 0 0 3px #4fc3f733}.input--sm{padding:.5rem .75rem;font-size:.875rem;min-height:32px}.input--md{padding:.75rem 1rem;font-size:1rem;min-height:40px}.input--lg{padding:1rem 1.25rem;font-size:1.125rem;min-height:48px}.input--default{background:var(--color-background-secondary)}.input--filled{background:#4fc3f71a;border-color:transparent}.input--filled:focus{background:var(--color-background-secondary);border-color:var(--color-primary)}.input--outlined{background:transparent;border-width:2px}.input--error{border-color:var(--error-500)}.input--error:focus{border-color:var(--error-500);box-shadow:0 0 0 3px #ef444433}.input--success{border-color:var(--success-500)}.input--success:focus{border-color:var(--success-500);box-shadow:0 0 0 3px #22c55e33}.input--warning{border-color:var(--warning-500)}.input--warning:focus{border-color:var(--warning-500);box-shadow:0 0 0 3px #f59e0b33}.input--disabled{opacity:.5;cursor:not-allowed;background:#4fc3f70d}.input--has-left-icon{padding-left:2.5rem}.input--has-right-icon{padding-right:2.5rem}.input--has-prefix{border-top-left-radius:0;border-bottom-left-radius:0;border-left:none}.input--has-suffix{border-top-right-radius:0;border-bottom-right-radius:0;border-right:none}.input--clearable{padding-right:2.5rem}.input-icon{position:absolute;top:50%;transform:translateY(-50%);display:flex;align-items:center;justify-content:center;color:var(--color-text-secondary);pointer-events:none;z-index:1}.input-icon--left{left:.75rem}.input-icon--right{right:.75rem}.input-icon svg{width:18px;height:18px;stroke-width:2}.input-prefix,.input-suffix{padding:.75rem 1rem;background:#4fc3f71a;border:1px solid rgba(79,195,247,.3);color:var(--color-text-secondary);font-size:.875rem;font-weight:500;display:flex;align-items:center}.input-prefix{border-top-left-radius:8px;border-bottom-left-radius:8px;border-right:none}.input-suffix{border-top-right-radius:8px;border-bottom-right-radius:8px;border-left:none}.input-clear{position:absolute;right:.75rem;top:50%;transform:translateY(-50%);width:20px;height:20px;border:none;background:none;color:var(--color-text-secondary);cursor:pointer;border-radius:50%;display:flex;align-items:center;justify-content:center;transition:all .2s ease;z-index:2}.input-clear:hover{background:#4fc3f71a;color:var(--color-text-primary)}.input-clear:disabled{opacity:.5;cursor:not-allowed}.input-clear svg{width:14px;height:14px;stroke-width:2}.input-footer{display:flex;justify-content:space-between;align-items:flex-start;gap:.5rem;min-height:1.25rem}.input-message{color:var(--color-text-secondary);font-size:.75rem;line-height:1.4;flex:1}.input-message--error{color:var(--error-500)}.input-count{color:var(--color-text-secondary);font-size:.75rem;font-family:monospace;line-height:1.4;white-space:nowrap}.input-count--error{color:var(--error-500)}@media (max-width: 767px){.input--sm{padding:.5rem;font-size:.8rem;min-height:28px}.input--md{padding:.625rem .875rem;font-size:.9rem;min-height:36px}.input--lg{padding:.875rem 1rem;font-size:1rem;min-height:44px}.input--has-left-icon{padding-left:2.25rem}.input--has-right-icon{padding-right:2.25rem}.input-icon--left{left:.5rem}.input-icon--right{right:.5rem}.input-icon svg{width:16px;height:16px}.input-clear{right:.5rem;width:18px;height:18px}.input-clear svg{width:12px;height:12px}.input-prefix,.input-suffix{padding:.625rem .875rem;font-size:.8rem}}@media (prefers-contrast: high){.input{border-width:2px}.input--outlined{border-width:3px}.input-prefix,.input-suffix{border-width:2px}}@media (prefers-reduced-motion: reduce){.input,.input-clear{transition:none}}.modal-wrapper{position:fixed;top:0;right:0;bottom:0;left:0;display:flex;align-items:center;justify-content:center;visibility:hidden;opacity:0;transition:all .3s ease}.modal-wrapper--open{visibility:visible;opacity:1}.modal-mask{position:absolute;top:0;right:0;bottom:0;left:0;background:#0009;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.modal{position:relative;background:var(--color-background-secondary);border-radius:16px;border:1px solid rgba(79,195,247,.2);box-shadow:0 20px 60px #0006;max-height:90vh;display:flex;flex-direction:column}.modal--sm{max-width:400px}.modal--md{max-width:500px}.modal--lg{max-width:700px}.modal--xl{max-width:900px}.modal--full{width:95vw;height:95vh;max-width:none;max-height:none}.modal-header{display:flex;justify-content:space-between;align-items:center;padding:1.5rem;border-bottom:1px solid rgba(79,195,247,.1)}.modal-title{color:var(--color-text-primary);font-size:1.25rem;font-weight:600;margin:0}.modal-close{width:32px;height:32px;border:none;background:none;color:var(--color-text-secondary);cursor:pointer;border-radius:8px;display:flex;align-items:center;justify-content:center}.modal-close:hover{background:#4fc3f71a;color:var(--color-text-primary)}.modal-close svg{width:18px;height:18px}.modal-body{flex:1;padding:1.5rem;overflow-y:auto;color:var(--color-text-primary)}.modal-footer{padding:1rem 1.5rem 1.5rem;border-top:1px solid rgba(79,195,247,.1);display:flex;justify-content:flex-end;gap:.75rem}.workout-page{min-height:100vh;background:var(--bg-primary);padding:2rem 1rem}@media (max-width: 767px){.workout-page{padding:1rem .5rem}}.workout-page .page-header{text-align:center;margin-bottom:2rem}.workout-page .page-header h1{color:var(--text-primary);font-size:2.5rem;font-weight:700;margin-bottom:.5rem;background:var(--gradient-brand);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.workout-page .page-header p{color:var(--text-secondary);font-size:1.1rem;opacity:.8}.workout-page .active-workout-section{margin-bottom:3rem}.workout-page .active-workout-section .active-workout-card{background:var(--bg-surface);border-radius:16px;padding:1.5rem;border:1px solid var(--primary-500);box-shadow:var(--shadow-lg)}.workout-page .active-workout-section .active-workout-card .workout-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem;padding-bottom:1rem;border-bottom:1px solid var(--primary-400)}@media (max-width: 767px){.workout-page .active-workout-section .active-workout-card .workout-header{flex-direction:column;gap:1rem;align-items:stretch}}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info{display:flex;flex-direction:column;gap:.5rem}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info h2{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin:0}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer{display:flex;align-items:center;gap:1rem}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer .timer-display{font-size:1.25rem;font-weight:600;color:var(--accent-500);font-family:Monaco,Menlo,monospace}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer .timer-btn{width:40px;height:40px;border-radius:50%;background:var(--gradient-primary);border:none;color:var(--text-on-accent);cursor:pointer;display:flex;align-items:center;justify-content:center;transition:all .2s ease}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer .timer-btn svg{width:18px;height:18px}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer .timer-btn:hover{transform:scale(1.05);box-shadow:var(--shadow-md)}.workout-page .active-workout-section .active-workout-card .workout-header .workout-info .workout-timer .timer-btn.pause svg{stroke-width:2.5}.workout-page .active-workout-section .active-workout-card .workout-header .finish-btn{background:var(--gradient-success);color:var(--text-on-success);border:none;padding:.75rem 1.5rem;border-radius:8px;font-weight:600;cursor:pointer;transition:all .2s ease}.workout-page .active-workout-section .active-workout-card .workout-header .finish-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-md)}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout{text-align:center;padding:3rem 1rem}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout .empty-icon{width:80px;height:80px;margin:0 auto 1.5rem;background:var(--gradient-primary);border-radius:50%;display:flex;align-items:center;justify-content:center}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout .empty-icon svg{width:40px;height:40px;stroke:var(--text-on-accent);stroke-width:2}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout h3{color:var(--text-primary);font-size:1.5rem;margin-bottom:.5rem}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout p{color:var(--text-secondary);margin-bottom:2rem;opacity:.8}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout .add-exercise-btn{background:var(--gradient-primary);color:var(--text-on-accent);border:none;padding:1rem 2rem;border-radius:8px;font-weight:600;cursor:pointer;display:inline-flex;align-items:center;gap:.5rem;transition:all .2s ease}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout .add-exercise-btn svg{width:20px;height:20px}.workout-page .active-workout-section .active-workout-card .workout-content .empty-workout .add-exercise-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-md)}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item{background:var(--bg-surface);border:1px solid var(--primary-400);border-radius:12px;padding:1rem;margin-bottom:1rem}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item h4{color:var(--text-primary);margin-bottom:1rem;font-weight:600}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row{display:grid;grid-template-columns:30px 1fr auto 1fr auto 40px;gap:.5rem;align-items:center;margin-bottom:.5rem;padding:.5rem;background:#4fc3f708;border-radius:8px}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .set-number{color:var(--color-text-secondary);font-weight:600;text-align:center}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .weight-input,.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .reps-input{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:6px;padding:.5rem;color:var(--text-primary);text-align:center;font-weight:600}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .weight-input:focus,.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .reps-input:focus{outline:none;border-color:var(--accent-500);box-shadow:var(--focus-ring)}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .unit{color:var(--text-secondary);font-size:.9rem}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .complete-btn{width:32px;height:32px;border-radius:50%;border:2px solid var(--primary-400);background:transparent;color:transparent;cursor:pointer;transition:all .2s ease}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .complete-btn.completed{background:var(--gradient-success);border-color:var(--success-500);color:var(--text-on-success)}.workout-page .active-workout-section .active-workout-card .workout-content .exercises-list .exercise-item .sets-grid .set-row .complete-btn:hover{transform:scale(1.1)}.workout-page .quick-start-section{margin-bottom:3rem}.workout-page .quick-start-section h2{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin-bottom:1.5rem;text-align:center}.workout-page .quick-start-section .quick-start-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:1.5rem}@media (max-width: 1024px){.workout-page .quick-start-section .quick-start-grid{grid-template-columns:repeat(auto-fit,minmax(250px,1fr));gap:1.25rem}}@media (max-width: 767px){.workout-page .quick-start-section .quick-start-grid{grid-template-columns:1fr;gap:1rem}}@media (max-width: 480px){.workout-page .quick-start-section .quick-start-grid{gap:.75rem}}.workout-page .quick-start-section .quick-start-grid .quick-start-card{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:16px;padding:2rem;text-align:center;cursor:pointer;transition:all .3s ease}.workout-page .quick-start-section .quick-start-grid .quick-start-card:hover{transform:translateY(-4px);border-color:var(--accent-500);box-shadow:var(--shadow-lg)}.workout-page .quick-start-section .quick-start-grid .quick-start-card .card-icon{width:60px;height:60px;margin:0 auto 1rem;background:var(--gradient-primary);border-radius:12px;display:flex;align-items:center;justify-content:center}.workout-page .quick-start-section .quick-start-grid .quick-start-card .card-icon svg{width:30px;height:30px;stroke:var(--text-on-accent);stroke-width:2}.workout-page .quick-start-section .quick-start-grid .quick-start-card h3{color:var(--text-primary);font-size:1.25rem;font-weight:600;margin-bottom:.5rem}.workout-page .quick-start-section .quick-start-grid .quick-start-card p{color:var(--text-secondary);opacity:.8}.workout-page .quick-start-section .quick-start-grid .quick-start-card.routine .card-icon{background:var(--gradient-brand)}.workout-page .quick-start-section .quick-start-grid .quick-start-card.previous .card-icon{background:var(--gradient-warning)}.workout-page .stats-section{margin-bottom:3rem}.workout-page .stats-section h2{color:var(--text-primary);font-size:1.5rem;font-weight:600;margin-bottom:1.5rem;text-align:center}.workout-page .stats-section .stats-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1.5rem}@media (max-width: 1024px){.workout-page .stats-section .stats-grid{grid-template-columns:repeat(auto-fit,minmax(180px,1fr));gap:1.25rem}}@media (max-width: 767px){.workout-page .stats-section .stats-grid{grid-template-columns:repeat(2,1fr);gap:1rem}}@media (max-width: 480px){.workout-page .stats-section .stats-grid{grid-template-columns:1fr;gap:.75rem}}.workout-page .stats-section .stats-grid .stat-card{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:16px;padding:1.5rem;text-align:center;transition:all .2s ease}.workout-page .stats-section .stats-grid .stat-card:hover{transform:translateY(-2px);box-shadow:var(--shadow-md)}.workout-page .stats-section .stats-grid .stat-card .stat-icon{width:50px;height:50px;margin:0 auto 1rem;background:var(--gradient-primary);border-radius:12px;display:flex;align-items:center;justify-content:center}.workout-page .stats-section .stats-grid .stat-card .stat-icon svg{width:24px;height:24px;stroke:var(--text-on-accent);stroke-width:2}.workout-page .stats-section .stats-grid .stat-card .stat-content .stat-value{display:block;color:var(--text-primary);font-size:2rem;font-weight:700;margin-bottom:.25rem}.workout-page .stats-section .stats-grid .stat-card .stat-content .stat-label{color:var(--text-secondary);font-size:.9rem;opacity:.8}.workout-page .recent-workouts-section .section-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:1.5rem}.workout-page .recent-workouts-section .section-header h2{color:var(--color-text-primary);font-size:1.5rem;font-weight:600;margin:0}.workout-page .recent-workouts-section .section-header .view-all-btn{background:none;border:none;color:var(--color-primary);font-weight:600;cursor:pointer;display:flex;align-items:center;gap:.25rem;transition:all .2s ease}.workout-page .recent-workouts-section .section-header .view-all-btn svg{width:16px;height:16px}.workout-page .recent-workouts-section .section-header .view-all-btn:hover{opacity:.8}.workout-page .recent-workouts-section .workouts-list .workout-item{background:var(--color-background-secondary);border:1px solid rgba(79,195,247,.2);border-radius:16px;padding:1.5rem;margin-bottom:1rem;display:flex;gap:1rem;align-items:center;transition:all .2s ease}.workout-page .recent-workouts-section .workouts-list .workout-item:hover{transform:translate(4px);border-color:var(--color-primary)}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-date{min-width:60px;text-align:center;background:linear-gradient(135deg,#4fc3f7,#29b6f6);color:#fff;border-radius:12px;padding:.75rem .5rem}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-date .month{display:block;font-size:.8rem;font-weight:600;text-transform:uppercase;opacity:.9}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-date .day{display:block;font-size:1.5rem;font-weight:700;margin-top:.25rem}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details{flex:1}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details h3{color:var(--color-text-primary);font-size:1.1rem;font-weight:600;margin-bottom:.5rem}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta{display:flex;gap:1rem;margin-bottom:.75rem}@media (max-width: 767px){.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta{flex-direction:column;gap:.25rem}}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta .duration,.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta .exercises-count{display:flex;align-items:center;gap:.25rem;color:var(--color-text-secondary);font-size:.9rem}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta .duration svg,.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .workout-meta .exercises-count svg{width:14px;height:14px}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .exercises-preview{display:flex;flex-wrap:wrap;gap:.5rem}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .exercises-preview .exercise-tag{background:#4fc3f71a;color:var(--color-primary);padding:.25rem .5rem;border-radius:4px;font-size:.8rem;font-weight:500}.workout-page .recent-workouts-section .workouts-list .workout-item .workout-details .exercises-preview .more-exercises{color:var(--color-text-secondary);font-size:.8rem;opacity:.6}.workout-page .recent-workouts-section .workouts-list .workout-item .action-btn{background:none;border:none;color:var(--color-text-secondary);cursor:pointer;padding:.5rem;border-radius:8px;transition:all .2s ease}.workout-page .recent-workouts-section .workouts-list .workout-item .action-btn svg{width:20px;height:20px}.workout-page .recent-workouts-section .workouts-list .workout-item .action-btn:hover{background:#4fc3f71a;color:var(--color-primary)}@media (min-width: 768px) and (max-width: 1023px){.workout-page{padding:1.5rem}.workout-page .stats-grid,.workout-page .quick-start-grid{grid-template-columns:repeat(2,1fr)}}@media (max-width: 767px){.workout-page .stats-grid{grid-template-columns:1fr 1fr;gap:.75rem}.workout-page .stats-grid .stat-card{padding:1rem}.workout-page .stats-grid .stat-card .stat-icon{width:40px;height:40px}.workout-page .stats-grid .stat-card .stat-icon svg{width:20px;height:20px}.workout-page .stats-grid .stat-card .stat-content .stat-value{font-size:1.5rem}.workout-page .workout-item{flex-direction:column;align-items:flex-start!important;gap:1rem}.workout-page .workout-item .workout-date{align-self:flex-start}}.mini-apple-watch-rings{display:flex;align-items:center;justify-content:center;position:relative}.mini-apple-watch-rings__svg{display:block;transform:rotate(0);transition:transform .3s ease}.mini-apple-watch-rings__progress-ring{transition:all .3s cubic-bezier(.4,0,.2,1);animation:progressAnimation 1.5s ease-out forwards}.mini-apple-watch-rings__progress-ring.completed{opacity:1}.mini-apple-watch-rings:hover .mini-apple-watch-rings__svg{transform:scale(1.05)}.mini-apple-watch-rings:hover .mini-apple-watch-rings__progress-ring{opacity:.8}@keyframes progressAnimation{0%{stroke-dashoffset:var(--circumference, 565.48)}to{stroke-dashoffset:var(--progress-offset, 0)}}@media (prefers-contrast: high){.mini-apple-watch-rings__progress-ring{stroke-width:4}}@media (prefers-reduced-motion: reduce){.mini-apple-watch-rings__svg{transition:none}.mini-apple-watch-rings__progress-ring{animation:none;transition:none}.mini-apple-watch-rings:hover .mini-apple-watch-rings__svg{transform:none}}@media (max-width: 768px){.mini-apple-watch-rings:hover .mini-apple-watch-rings__svg{transform:scale(1.02)}}@media (max-width: 480px){.mini-apple-watch-rings:hover .mini-apple-watch-rings__svg{transform:scale(1.01)}}.weekly-date-picker-card{background:var(--bg-primary);border-radius:24px;padding:24px;margin:0 auto 20px;max-width:min(92vw,450px);width:100%;box-shadow:0 2px 16px #00000014;border:1px solid var(--border-color)}.weekly-date-picker-card__header{display:flex;justify-content:space-between;align-items:center;margin-bottom:32px}.weekly-date-picker-card__title{font-size:28px;font-weight:700;color:var(--text-primary);margin:0;letter-spacing:-.5px}.weekly-date-picker-card__nav-btn{display:flex;align-items:center;justify-content:center;width:36px;height:36px;border:none;background:var(--bg-surface);border-radius:12px;cursor:pointer;transition:all .2s ease;color:var(--text-secondary)}.weekly-date-picker-card__nav-btn:hover{background:var(--bg-hover);color:var(--text-primary);transform:translate(2px)}.weekly-date-picker-card__calendar{display:flex;justify-content:space-between;gap:8px;margin-bottom:32px}.weekly-date-picker-card__date-item{display:flex;flex-direction:column;align-items:center;cursor:pointer;transition:all .2s ease}.weekly-date-picker-card__date-item:hover:not(.disabled){transform:translateY(-2px)}.weekly-date-picker-card__date-item.disabled{cursor:not-allowed;opacity:.5}.weekly-date-picker-card__weekday{font-size:12px;font-weight:500;color:var(--text-tertiary);text-align:center;letter-spacing:.2px;margin-bottom:8px;padding:4px 8px;border-radius:12px;background:transparent;transition:all .2s ease;min-width:52px}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card__weekday{color:var(--text-secondary);font-weight:500}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__weekday{font-weight:700;color:var(--accent-500);background:#3b82f61a}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__weekday{font-weight:700;color:#fff!important;background:var(--accent-600);box-shadow:0 2px 8px #3b82f640}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card__weekday{color:var(--text-disabled);opacity:.8}.weekly-date-picker-card__date-circle{position:relative;width:64px;height:64px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:var(--bg-surface);border:2px solid transparent;transition:all .2s ease}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color);opacity:.8}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:transparent}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__date-circle{background:var(--accent-500);border-color:var(--accent-500);border-width:2px;box-shadow:0 4px 12px #3b82f64d;transform:scale(1.15)}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color);opacity:.7}.weekly-date-picker-card__day-number{font-size:16px;font-weight:600;color:var(--text-secondary);z-index:3;position:relative;transition:all .2s ease}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card__day-number{color:var(--text-secondary);font-weight:600}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__day-number{color:var(--accent-500);font-weight:700;font-size:18px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__day-number{color:#000!important;font-size:22px;font-weight:700;text-shadow:none}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card__day-number{color:var(--text-disabled);opacity:.8}.weekly-date-picker-card__date-item.has-data:not(.selected) .weekly-date-picker-card__day-number{font-size:13px;font-weight:600}.weekly-date-picker-card__rings{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);z-index:2;opacity:.9}.weekly-date-picker-card__rings .mini-apple-watch-rings{transform:scale(1.4)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__rings{opacity:1;transform:translate(-50%,-50%) scale(1.5)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card__rings{opacity:.7;transform:translate(-50%,-50%) scale(1.3)}.weekly-date-picker-card__date-item:hover:not(.disabled) .weekly-date-picker-card__rings{opacity:1;transform:translate(-50%,-50%) scale(1.6)}.weekly-date-picker-card__stats{display:flex;align-items:center;padding-top:24px;border-top:1px solid var(--border-color)}.weekly-date-picker-card__stat-item{display:flex;align-items:center;gap:16px;flex:1}.weekly-date-picker-card__stat-icon{font-size:20px;width:24px;text-align:center}.weekly-date-picker-card__stat-content{display:flex;align-items:baseline;gap:4px;flex-wrap:wrap}.weekly-date-picker-card__stat-number{font-size:32px;font-weight:700;color:var(--text-primary);line-height:1}.weekly-date-picker-card__stat-unit{font-size:18px;color:var(--text-tertiary);font-weight:500}.weekly-date-picker-card__stat-label{font-size:18px;color:var(--text-primary);font-weight:600}.weekly-date-picker-card__stat-description{font-size:14px;color:var(--text-secondary);width:100%;margin-top:2px}.weekly-date-picker-card__stat-divider{width:1px;height:48px;background:var(--border-color);margin:0 24px}@media (max-width: 768px){.weekly-date-picker-card{padding:20px;border-radius:20px;margin:0 auto 16px;max-width:min(95vw,400px)}.weekly-date-picker-card__header{margin-bottom:24px}.weekly-date-picker-card__title{font-size:24px}.weekly-date-picker-card__nav-btn{width:32px;height:32px;border-radius:10px}.weekly-date-picker-card__calendar{gap:4px;margin-bottom:24px}.weekly-date-picker-card__weekday{font-size:11px;margin-bottom:6px;padding:3px 6px;min-width:44px}.weekly-date-picker-card__date-circle{width:52px;height:52px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__date-circle{transform:scale(1.1)}.weekly-date-picker-card__day-number{font-size:14px}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__day-number{font-size:16px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__day-number{font-size:18px}.weekly-date-picker-card__date-item.has-data:not(.selected) .weekly-date-picker-card__day-number{font-size:12px}.weekly-date-picker-card__rings .mini-apple-watch-rings{transform:scale(1.2)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.3)}.weekly-date-picker-card__date-item:hover:not(.disabled) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.4)}.weekly-date-picker-card__stats{padding-top:20px}.weekly-date-picker-card__stat-item{gap:12px}.weekly-date-picker-card__stat-icon{font-size:18px}.weekly-date-picker-card__stat-number{font-size:28px}.weekly-date-picker-card__stat-unit,.weekly-date-picker-card__stat-label{font-size:16px}.weekly-date-picker-card__stat-description{font-size:13px}.weekly-date-picker-card__stat-divider{height:40px;margin:0 16px}}@media (max-width: 480px){.weekly-date-picker-card{padding:16px;border-radius:16px;margin:0 auto 16px;max-width:min(96vw,350px)}.weekly-date-picker-card__header{margin-bottom:20px}.weekly-date-picker-card__title{font-size:22px}.weekly-date-picker-card__nav-btn{width:28px;height:28px;border-radius:8px}.weekly-date-picker-card__calendar{gap:2px;margin-bottom:20px}.weekly-date-picker-card__weekday{font-size:10px;margin-bottom:4px;padding:2px 4px;min-width:36px}.weekly-date-picker-card__date-circle{width:44px;height:44px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__date-circle{transform:scale(1.05)}.weekly-date-picker-card__day-number{font-size:12px}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__day-number{font-size:14px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__day-number{font-size:16px}.weekly-date-picker-card__date-item.has-data:not(.selected) .weekly-date-picker-card__day-number{font-size:9px}.weekly-date-picker-card__rings .mini-apple-watch-rings{transform:scale(1)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.15)}.weekly-date-picker-card__date-item:hover:not(.disabled) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.25)}.weekly-date-picker-card__stats{padding-top:16px;display:flex;flex-direction:row;gap:16px}.weekly-date-picker-card__stat-item{gap:12px;flex:1}.weekly-date-picker-card__stat-number{font-size:24px}.weekly-date-picker-card__stat-unit,.weekly-date-picker-card__stat-label{font-size:14px}.weekly-date-picker-card__stat-description{font-size:12px}.weekly-date-picker-card__stat-divider{display:block;height:40px;margin:0 8px}}.weekly-date-picker-card.theme-dark{background:var(--bg-primary);border-color:var(--border-color);box-shadow:0 2px 16px #0000004d}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__title{color:var(--text-primary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__nav-btn{background:var(--bg-surface);color:var(--text-secondary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__nav-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__weekday{color:var(--text-tertiary)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__weekday{color:var(--text-secondary)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__weekday{color:var(--accent-400);background:#60a5fa26}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-dark .weekly-date-picker-card__weekday{color:#fff;background:var(--accent-500)}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-dark .weekly-date-picker-card__weekday{color:var(--text-disabled)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__date-circle{background:var(--bg-surface)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:transparent}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-dark .weekly-date-picker-card__date-circle{background:linear-gradient(135deg,var(--accent-500),var(--accent-400));border-color:var(--accent-500)}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-dark .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__day-number{color:var(--text-secondary)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__day-number{color:var(--text-secondary)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-dark .weekly-date-picker-card__day-number{color:var(--accent-400)}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-dark .weekly-date-picker-card__day-number{color:#fff}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-dark .weekly-date-picker-card__day-number{color:var(--text-disabled)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stats{border-top-color:var(--border-color)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stat-number{color:var(--text-primary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stat-unit{color:var(--text-tertiary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stat-label{color:var(--text-primary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stat-description{color:var(--text-secondary)}.weekly-date-picker-card.theme-dark .weekly-date-picker-card__stat-divider{background:var(--border-color)}.weekly-date-picker-card.theme-light{background:var(--bg-primary);border-color:var(--border-color);box-shadow:0 2px 16px #0000000d}.weekly-date-picker-card.theme-light .weekly-date-picker-card__title{color:var(--text-primary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__nav-btn{background:var(--bg-surface);color:var(--text-secondary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__nav-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__weekday{color:var(--text-tertiary)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__weekday{color:var(--text-secondary)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__weekday{color:var(--accent-600);background:#3b82f614}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-light .weekly-date-picker-card__weekday{color:#fff;background:var(--accent-600)}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-light .weekly-date-picker-card__weekday{color:var(--text-disabled)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__date-circle{background:var(--bg-surface)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:transparent}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-light .weekly-date-picker-card__date-circle{background:var(--accent-600);border-color:var(--accent-600);box-shadow:0 4px 12px #3b82f640}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-light .weekly-date-picker-card__date-circle{background:var(--bg-surface);border-color:var(--border-color)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__day-number{color:var(--text-secondary)}.weekly-date-picker-card__date-item.past:not(.today):not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__day-number{color:var(--text-secondary)}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card.theme-light .weekly-date-picker-card__day-number{color:var(--accent-600)}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card.theme-light .weekly-date-picker-card__day-number{color:#fff}.weekly-date-picker-card__date-item.future.disabled .weekly-date-picker-card.theme-light .weekly-date-picker-card__day-number{color:var(--text-disabled)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stats{border-top-color:var(--border-color)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stat-number{color:var(--text-primary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stat-unit{color:var(--text-tertiary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stat-label{color:var(--text-primary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stat-description{color:var(--text-secondary)}.weekly-date-picker-card.theme-light .weekly-date-picker-card__stat-divider{background:var(--border-color)}@media (prefers-contrast: high){.weekly-date-picker-card{border-width:2px}.weekly-date-picker-card__date-circle{border-width:3px}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__date-circle{border-width:3px;box-shadow:0 0 0 2px #3b82f680}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__date-circle{border-width:2px;border-color:var(--accent-600);box-shadow:0 0 0 2px #3b82f666}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__weekday{background:#000;color:#fff;border:2px solid #000}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__weekday{background:#3b82f633}.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__day-number{color:#fff;font-weight:800}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__day-number{font-weight:800}.weekly-date-picker-card__stats{border-top-width:2px}.weekly-date-picker-card__stat-divider{width:2px}}@media (prefers-reduced-motion: reduce){.weekly-date-picker-card__date-item,.weekly-date-picker-card__date-circle,.weekly-date-picker-card__day-number,.weekly-date-picker-card__weekday,.weekly-date-picker-card__rings,.weekly-date-picker-card__nav-btn{transition:none}.weekly-date-picker-card__date-item:hover:not(.disabled){transform:none}.weekly-date-picker-card__nav-btn:hover,.weekly-date-picker-card__date-item.selected .weekly-date-picker-card__date-circle{transform:none}.weekly-date-picker-card__date-item.today:not(.selected) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.5)}.weekly-date-picker-card__date-item:hover:not(.disabled) .weekly-date-picker-card__rings{transform:translate(-50%,-50%) scale(1.6)}}.fitness-progress-card{background:var(--bg-surface, #ffffff);border-radius:16px;padding:20px;margin:0 auto 20px;max-width:min(92vw,450px);width:100%;box-shadow:0 4px 12px #0000001a;transition:all .3s ease;display:flex;flex-direction:row;align-items:stretch;gap:20px;min-height:280px}.fitness-progress-card.theme-dark{background:var(--bg-surface);border:1px solid var(--border-color);box-shadow:0 4px 12px #0000004d;--ring-background: rgba(255, 255, 255, .1);--text-primary: #f8fafc;--text-secondary: #cbd5e1;--border-light: rgba(255, 255, 255, .1)}.fitness-progress-card.theme-light{--ring-background: #E5E5E5;--text-primary: #1a1a1a;--text-secondary: #6b7280;--border-light: #e5e7eb}.fitness-progress-card__rings{flex:0 0 50%;display:flex;justify-content:center;align-items:center;position:relative}.fitness-progress-card__rings-container{position:relative;width:200px;height:200px;display:flex;justify-content:center;align-items:center}.fitness-progress-card__rings .fitness-progress-card__svg{width:100%;height:100%;max-width:200px;max-height:200px}.fitness-progress-card__rings .fitness-progress-card__progress-ring{transition:stroke-dasharray 1s cubic-bezier(.4,0,.2,1)}.fitness-progress-card__center-data{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);text-align:center;pointer-events:none}.fitness-progress-card__main-stat{display:flex;flex-direction:column;align-items:center;justify-content:center}.fitness-progress-card__main-stat .value{font-size:26px;font-weight:700;color:var(--text-primary);line-height:1;margin-bottom:2px}.fitness-progress-card__main-stat .unit{font-size:11px;color:var(--text-secondary);font-weight:500;line-height:1}.fitness-progress-card__stats{flex:0 0 50%;display:flex;flex-direction:column;padding-left:20px;padding-right:16px;border-left:1px solid var(--border-light)}.fitness-progress-card__stats-list{flex:1;display:flex;flex-direction:column;justify-content:space-around;gap:16px}.fitness-progress-card__stat-item{display:flex;align-items:center;gap:12px;padding:12px;border-radius:12px;background:var(--bg-hover, rgba(248, 250, 252, .5));transition:all .2s ease}.fitness-progress-card__stat-item:hover{transform:translateY(-1px);box-shadow:0 2px 8px #0000001a}.fitness-progress-card__stat-color{width:12px;height:12px;border-radius:50%;flex-shrink:0}.fitness-progress-card__stat-info{flex:1;min-width:0}.fitness-progress-card__stat-label{font-size:14px;font-weight:600;color:var(--text-primary);margin-bottom:4px;display:flex;align-items:center;gap:6px}.fitness-progress-card__stat-label .completed-icon{color:#22c55e;font-size:12px}.fitness-progress-card__stat-progress{font-size:13px;color:var(--text-secondary)}.fitness-progress-card__stat-progress .current{font-weight:600;color:var(--text-primary)}.fitness-progress-card__stat-progress .separator{margin:0 2px}.fitness-progress-card__stat-progress .goal{font-weight:500}.fitness-progress-card__stat-progress .unit{margin-left:2px}.fitness-progress-card__stat-progress .percentage{margin-left:6px;font-weight:500;color:var(--text-secondary)}@media (max-width: 768px){.fitness-progress-card{max-width:min(95vw,400px);min-height:320px;gap:16px;padding:16px}.fitness-progress-card__rings-container{width:180px;height:180px}.fitness-progress-card__main-stat .value{font-size:24px;margin-bottom:2px}.fitness-progress-card__main-stat .unit{font-size:10px;line-height:1}.fitness-progress-card__stats{padding-left:16px;padding-right:12px}.fitness-progress-card__stats-list{gap:12px}.fitness-progress-card__stat-item{padding:10px;gap:10px}.fitness-progress-card__stat-color{width:10px;height:10px}.fitness-progress-card__stat-label{font-size:13px}.fitness-progress-card__stat-progress{font-size:12px}}@media (max-width: 480px){.fitness-progress-card{max-width:min(96vw,350px);padding:12px;gap:12px;min-height:200px}.fitness-progress-card__rings{flex:0 0 50%}.fitness-progress-card__rings-container{width:120px;height:120px}.fitness-progress-card__main-stat .value{font-size:18px;margin-bottom:1px}.fitness-progress-card__main-stat .unit{font-size:9px;line-height:1}.fitness-progress-card__stats{flex:0 0 50%;padding-left:12px;padding-right:8px;border-left:1px solid var(--border-light)}.fitness-progress-card__stats-list{gap:8px}.fitness-progress-card__stat-item{padding:8px}.fitness-progress-card__stat-color{width:8px;height:8px}.fitness-progress-card__stat-label{font-size:12px}.fitness-progress-card__stat-progress{font-size:10px}}.character-task-card{background:var(--bg-surface, #ffffff);border-radius:16px;padding:16px!important;margin:0 auto 20px;max-width:min(92vw,450px)!important;width:100%;box-shadow:0 4px 12px #0000001a;transition:all .3s ease;overflow:hidden!important}.character-task-card *,.character-task-card *:before,.character-task-card *:after{box-sizing:border-box!important}.character-task-card.theme-dark{background:var(--bg-surface);border:1px solid var(--border-color);box-shadow:0 4px 12px #0000004d}.character-task-card.theme-dark .progress-bar{background-color:#1a1a1a;border-color:#000}.character-task-card.theme-dark .progress-bar .progress-fill.tasks{background:#0c0;box-shadow:inset 0 1px #4f4,inset 0 -1px #060}.character-task-card.theme-dark .progress-bar .progress-fill.energy{background:#c50;box-shadow:inset 0 1px #f72,inset 0 -1px #930}.character-task-card__status-bar{display:flex;justify-content:space-between;align-items:center;padding:8px 0 12px;border-bottom:1px solid var(--border-color, rgba(0, 0, 0, .1));margin-bottom:12px}.character-task-card__status-bar .status-left .status-title{font-size:18px;font-weight:600;color:var(--text-primary)}.character-task-card__status-bar .status-right{display:flex;align-items:center;gap:20px}.character-task-card__status-bar .status-right .stat-item{display:flex;align-items:center;justify-content:center;gap:6px}.character-task-card__status-bar .status-right .stat-item .stat-icon{width:20px;height:20px;object-fit:contain}.character-task-card__status-bar .status-right .stat-item .stat-value{font-size:16px;font-weight:600;color:var(--text-primary)}.character-task-card__header{display:flex!important;flex-direction:row!important;align-items:flex-start!important;gap:16px!important;margin-bottom:20px;width:100%!important;box-sizing:border-box!important}.character-task-card__header .left-section{flex:0 0 45%!important;display:flex;flex-direction:column;align-items:center;position:relative;box-sizing:border-box!important}.character-task-card__header .left-section .character-info{text-align:center;padding:8px!important;border-radius:12px;width:100%}.character-task-card__header .left-section .character-info .motivation{font-size:14px;color:var(--text-secondary);margin:0 0 12px}.character-task-card__header .left-section .character-info .character-avatar{display:flex;justify-content:center;align-items:center}.character-task-card__header .left-section .character-info .character-avatar .character-image{width:160px;height:160px;object-fit:cover;border-radius:50%}.character-task-card__header .right-section{flex:0 0 55%!important;display:flex;flex-direction:column;justify-content:space-around;padding:0 8px 0 0!important;box-sizing:border-box!important;max-width:55%!important;margin-top:22px;height:160px}.character-task-card__header .right-section .progress-section{width:100%;flex-shrink:0;margin-bottom:8px}.character-task-card__header .right-section .progress-section .progress-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:4px!important;font-size:14px;font-weight:600;width:100%;overflow:hidden}.character-task-card__header .right-section .progress-section .progress-header .progress-title{color:var(--text-secondary);flex:1 1 auto;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.character-task-card__header .right-section .progress-section .progress-header .progress-value{color:var(--text-primary);flex:0 0 auto;text-align:right;min-width:70px;max-width:80px;font-size:13px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.character-task-card__header .right-section .progress-section .pixel-progress-bar{image-rendering:-moz-crisp-edges;image-rendering:-webkit-crisp-edges;image-rendering:pixelated;image-rendering:crisp-edges;display:block}.character-task-card__header .right-section .progress-section .progress-bar{width:92%!important;max-width:92%!important;height:8px;border-radius:0;background-color:#2a2a2a;border:2px solid #1a1a1a;box-shadow:inset 1px 1px #ffffff1a,inset -1px -1px #0000004d;overflow:hidden;box-sizing:border-box!important;position:relative}.character-task-card__header .right-section .progress-section .progress-bar .progress-fill{height:100%;border-radius:0;transition:width .8s cubic-bezier(.25,.46,.45,.94);position:relative}.character-task-card__header .right-section .progress-section .progress-bar .progress-fill.tasks{background:#0f0;box-shadow:inset 0 1px #4f4,inset 0 -1px #0a0}.character-task-card__header .right-section .progress-section .progress-bar .progress-fill.tasks:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background-image:linear-gradient(90deg,transparent 0px,transparent 1px,rgba(255,255,255,.1) 1px,rgba(255,255,255,.1) 2px);background-size:2px 100%}.character-task-card__header .right-section .progress-section .progress-bar .progress-fill.energy{background:#f60;box-shadow:inset 0 1px #f94,inset 0 -1px #c40}.character-task-card__header .right-section .progress-section .progress-bar .progress-fill.energy:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;background-image:linear-gradient(90deg,transparent 0px,transparent 1px,rgba(255,255,255,.1) 1px,rgba(255,255,255,.1) 2px);background-size:2px 100%}.character-task-card__header .right-section .progress-section .progress-bar:before{content:"";position:absolute;top:-1px;left:-1px;right:-1px;bottom:-1px;background:linear-gradient(45deg,#444,#666,#444);z-index:-1;border-radius:0}.character-task-card__header .right-section .progress-section:last-child{margin-bottom:0!important}.character-task-card__tasks{margin-top:-12px}.character-task-card__tasks .tasks-title{font-size:18px;font-weight:600;color:var(--text-primary);margin:0 0 6px;padding:0 4px}.character-task-card__tasks .tasks-container{display:flex;flex-direction:column;gap:12px}.character-task-card__tasks .tasks-scroll{display:flex;gap:12px;padding:8px 4px 12px;overflow-x:auto;overflow-y:hidden;scroll-behavior:smooth;-webkit-overflow-scrolling:touch}.character-task-card__tasks .tasks-scroll::-webkit-scrollbar{height:4px}.character-task-card__tasks .tasks-scroll::-webkit-scrollbar-track{background:var(--bg-secondary);border-radius:2px}.character-task-card__tasks .tasks-scroll::-webkit-scrollbar-thumb{background:var(--border-color);border-radius:2px}.character-task-card__tasks .tasks-scroll::-webkit-scrollbar-thumb:hover{background:var(--text-secondary)}.character-task-card__tasks .task-card{background:var(--bg-hover, #f8fafc);border:1px solid var(--border-color, #e5e7eb);border-radius:12px;padding:14px;position:relative;transition:all .3s ease;display:flex;flex-direction:column;flex-shrink:0;width:calc((100% - 24px) / 3);min-width:130px;max-width:155px;height:220px}.character-task-card__tasks .task-card:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a;border-color:var(--accent-300, #c7d2fe)}.character-task-card__tasks .task-card.completed{background:linear-gradient(135deg,#dcfce7,#bbf7d0);border-color:#22c55e}.character-task-card__tasks .task-card .task-header{display:flex;flex-direction:column;align-items:center;gap:3px;margin-bottom:6px;text-align:center}.character-task-card__tasks .task-card .task-header .task-icon{font-size:18px;width:22px;height:22px;display:flex;align-items:center;justify-content:center;flex-shrink:0}.character-task-card__tasks .task-card .task-header .task-title{font-size:13px;font-weight:700;color:var(--text-primary);margin:0;line-height:1.3;text-align:center;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;width:100%}.character-task-card__tasks .task-card .task-description{font-size:9px;color:var(--text-secondary);margin:0 0 6px;line-height:1.4;overflow:hidden;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;height:36px;min-height:36px}.character-task-card__tasks .task-card .reward-display{display:flex;flex-direction:column;align-items:center;justify-content:center;margin:4px 0 6px}.character-task-card__tasks .task-card .reward-display .reward-plate{width:50px;height:50px;object-fit:contain;margin-bottom:4px;image-rendering:pixelated;transition:transform .2s ease}.character-task-card__tasks .task-card .reward-display .reward-plate:hover{transform:scale(1.1)}.character-task-card__tasks .task-card .reward-display .reward-text{font-size:10px;font-weight:600;color:var(--text-secondary);text-align:center;opacity:.8;white-space:nowrap}.character-task-card__tasks .task-card .task-footer{display:flex;justify-content:center;align-items:center;margin-top:auto;height:30px}.character-task-card__tasks .task-card .task-footer .task-button{border:none;border-radius:6px;color:#fff;font-weight:600;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all .2s ease;flex-shrink:0}.character-task-card__tasks .task-card .task-footer .task-button.completed{background:#22c55e;width:50px;height:22px;font-size:9px}.character-task-card__tasks .task-card .task-footer .task-button.start-button{background:var(--accent-600, #2563eb);width:34px;height:22px;font-size:9px;padding:3px 5px;white-space:nowrap}.character-task-card__tasks .task-card .task-footer .task-button.start-button:hover{background:var(--accent-700, #1d4ed8);transform:scale(1.05)}.character-task-card__tasks .task-card .task-footer .task-button:hover{transform:scale(1.05)}@media (max-width: 768px){.character-task-card{padding:16px;max-width:min(95vw,400px)}.character-task-card__status-bar{padding:6px 0 10px;margin-bottom:10px}.character-task-card__status-bar .status-left .status-title{font-size:16px}.character-task-card__status-bar .status-right{gap:16px}.character-task-card__status-bar .status-right .stat-item{gap:5px}.character-task-card__status-bar .status-right .stat-item .stat-icon{width:18px;height:18px}.character-task-card__status-bar .status-right .stat-item .stat-value{font-size:14px}.character-task-card__header{gap:16px;align-items:flex-start!important}.character-task-card__header .left-section{flex:0 0 45%!important}.character-task-card__header .left-section .character-info .greeting{font-size:14px}.character-task-card__header .left-section .character-info .motivation{font-size:12px;margin-bottom:10px}.character-task-card__header .left-section .character-info .character-avatar .character-image{width:150px;height:150px}.character-task-card__header .left-section .stats-section{gap:16px;margin-top:6px}.character-task-card__header .left-section .stats-section .stat-item{gap:6px}.character-task-card__header .left-section .stats-section .stat-item .stat-icon{width:19px;height:19px}.character-task-card__header .left-section .stats-section .stat-item .stat-value{font-size:14px}.character-task-card__header .right-section{flex:0 0 55%!important;justify-content:space-around;margin-top:18px;height:150px}.character-task-card__header .right-section .progress-section{margin-bottom:6px}.character-task-card__header .right-section .progress-section .progress-header{margin-bottom:3px;font-size:12px}.character-task-card__header .right-section .progress-section .progress-header .progress-title,.character-task-card__header .right-section .progress-section .progress-header .progress-value{font-size:12px}.character-task-card__header .right-section .progress-section .progress-header .progress-value{min-width:60px;max-width:70px}.character-task-card__header .right-section .progress-section .progress-bar{width:90%!important;max-width:90%!important;height:10px;border-radius:0;border-width:2px}.character-task-card__tasks .tasks-title{font-size:16px;margin-bottom:10px}.character-task-card__tasks .tasks-scroll{gap:10px;padding:6px 2px 10px}.character-task-card__tasks .task-card{width:calc((100% - 20px) / 3);min-width:110px;max-width:135px;height:190px;padding:12px}.character-task-card__tasks .task-card .task-header{margin-bottom:5px;gap:4px}.character-task-card__tasks .task-card .task-header .task-icon{font-size:14px;width:18px;height:18px}.character-task-card__tasks .task-card .task-header .task-title{font-size:10px}.character-task-card__tasks .task-card .task-description{font-size:8px;height:30px;min-height:30px;margin-bottom:5px;line-height:1.4;-webkit-line-clamp:3}.character-task-card__tasks .task-card .reward-display{margin:3px 0 5px}.character-task-card__tasks .task-card .reward-display .reward-plate{width:42px;height:42px;margin-bottom:3px}.character-task-card__tasks .task-card .reward-display .reward-text{font-size:9px}.character-task-card__tasks .task-card .task-footer{min-height:18px}.character-task-card__tasks .task-card .task-footer .task-button.completed{width:42px;height:18px;font-size:8px}.character-task-card__tasks .task-card .task-footer .task-button.start-button{width:28px;height:18px;font-size:8px;padding:2px 4px}}@media (max-width: 480px){.character-task-card{padding:12px;max-width:min(96vw,350px)}.character-task-card__status-bar{padding:4px 0 8px;margin-bottom:8px}.character-task-card__status-bar .status-left .status-title{font-size:14px}.character-task-card__status-bar .status-right{gap:12px}.character-task-card__status-bar .status-right .stat-item{gap:4px}.character-task-card__status-bar .status-right .stat-item .stat-icon{width:16px;height:16px}.character-task-card__status-bar .status-right .stat-item .stat-value{font-size:12px}.character-task-card__header{display:flex!important;flex-direction:row!important;align-items:flex-start!important;gap:12px!important;width:100%!important;box-sizing:border-box!important}.character-task-card__header .left-section{flex:0 0 42%!important}.character-task-card__header .left-section .character-info .greeting{font-size:13px}.character-task-card__header .left-section .character-info .motivation{font-size:11px;margin-bottom:8px}.character-task-card__header .left-section .character-info .character-avatar .character-image{width:120px;height:120px}.character-task-card__header .left-section .stats-section{gap:12px;margin-top:4px}.character-task-card__header .left-section .stats-section .stat-item{gap:4px}.character-task-card__header .left-section .stats-section .stat-item .stat-icon{width:17px;height:17px}.character-task-card__header .left-section .stats-section .stat-item .stat-value{font-size:12px}.character-task-card__header .right-section{flex:0 0 55%!important;justify-content:space-around;margin-top:15px;height:120px}.character-task-card__header .right-section .progress-section{margin-bottom:4px}.character-task-card__header .right-section .progress-section .progress-header{margin-bottom:2px;font-size:10px}.character-task-card__header .right-section .progress-section .progress-header .progress-title,.character-task-card__header .right-section .progress-section .progress-header .progress-value{font-size:10px}.character-task-card__header .right-section .progress-section .progress-header .progress-value{min-width:50px;max-width:60px}.character-task-card__header .right-section .progress-section .progress-bar{width:88%!important;max-width:88%!important;height:8px;border-radius:0;border-width:1px}.character-task-card__tasks .tasks-title{font-size:14px;margin-bottom:8px}.character-task-card__tasks .tasks-scroll{gap:8px;padding:4px 2px 8px}.character-task-card__tasks .task-card{width:calc((100% - 16px) / 3);min-width:95px;max-width:115px;height:160px;padding:10px}.character-task-card__tasks .task-card .task-header{margin-bottom:4px}.character-task-card__tasks .task-card .task-header .task-icon{font-size:12px;width:16px;height:16px}.character-task-card__tasks .task-card .task-header .task-title{font-size:9px}.character-task-card__tasks .task-card .task-description{font-size:7px;height:24px;min-height:24px;-webkit-line-clamp:3;margin-bottom:3px;line-height:1.4}.character-task-card__tasks .task-card .reward-display{margin:2px 0 4px}.character-task-card__tasks .task-card .reward-display .reward-plate{width:36px;height:36px;margin-bottom:2px}.character-task-card__tasks .task-card .reward-display .reward-text{font-size:8px}.character-task-card__tasks .task-card .task-footer{min-height:16px}.character-task-card__tasks .task-card .task-footer .task-button.completed{width:36px;height:16px;font-size:7px}.character-task-card__tasks .task-card .task-footer .task-button.start-button{width:24px;height:16px;font-size:7px;padding:2px 3px}}.user-recommendation-card{background:var(--bg-surface, #ffffff);border-radius:16px;padding:20px;margin:0 auto 20px;max-width:min(92vw,450px);width:100%;box-shadow:0 4px 12px #0000001a;transition:all .3s ease;overflow:visible}.user-recommendation-card.theme-dark{background:var(--bg-surface);border:1px solid var(--border-color);box-shadow:0 4px 12px #0000004d}.user-recommendation-card__header{display:flex;justify-content:space-between;align-items:center;margin-bottom:20px}.user-recommendation-card__header .section-title{font-size:18px;font-weight:700;color:var(--text-primary, #1f2937);margin:0}.user-recommendation-card__header .view-all-btn{background:none;border:none;color:var(--text-secondary, #6b7280);font-size:14px;font-weight:500;cursor:pointer;padding:4px 8px;border-radius:6px;transition:all .2s ease}.user-recommendation-card__header .view-all-btn:hover{background:var(--bg-hover, #f3f4f6);color:var(--text-primary)}.user-recommendation-card__content .users-scroll-container{overflow-x:auto;overflow-y:visible;-webkit-overflow-scrolling:touch;scroll-behavior:smooth;scrollbar-width:none;-ms-overflow-style:none}.user-recommendation-card__content .users-scroll-container::-webkit-scrollbar{display:none}.user-recommendation-card__content .users-list{display:flex;gap:16px;padding:8px 0 16px}.user-recommendation-card__content .users-list .user-item{position:relative;display:flex;flex-direction:column;align-items:center;padding:20px 16px;background:var(--bg-hover, #f8fafc);border-radius:16px;border:1px solid var(--border-color, #e5e7eb);transition:all .3s ease;min-width:180px;flex:0 0 calc(50% - 8px);max-width:200px;min-height:220px}.user-recommendation-card__content .users-list .user-item:hover{background:var(--bg-surface, #ffffff);border-color:var(--accent-300, #c7d2fe);transform:translateY(-4px);box-shadow:0 8px 24px #00000026}.user-recommendation-card__content .users-list .user-item .close-btn{position:absolute;top:8px;right:8px;background:none;border:none;color:var(--text-tertiary, #9ca3af);font-size:16px;font-weight:600;cursor:pointer;width:22px;height:22px;display:flex;align-items:center;justify-content:center;border-radius:50%;transition:all .2s ease}.user-recommendation-card__content .users-list .user-item .close-btn:hover{background:var(--bg-surface, #f3f4f6);color:var(--text-secondary)}.user-recommendation-card__content .users-list .user-item .user-avatar{width:80px;height:80px;border-radius:50%;overflow:hidden;flex-shrink:0;display:flex;align-items:center;justify-content:center;position:relative;margin-bottom:12px;border:3px solid var(--border-color, #e5e7eb)}.user-recommendation-card__content .users-list .user-item .user-avatar__image{width:100%;height:100%;object-fit:cover}.user-recommendation-card__content .users-list .user-item .user-avatar__initials{width:100%;height:100%;display:flex;align-items:center;justify-content:center;font-size:28px;font-weight:700;color:#fff;background:linear-gradient(135deg,#e879f9,#a855f7)}.user-recommendation-card__content .users-list .user-item:nth-child(1) .user-avatar__initials{background:linear-gradient(135deg,#f472b6,#ec4899)}.user-recommendation-card__content .users-list .user-item:nth-child(2) .user-avatar__initials{background:linear-gradient(135deg,#60a5fa,#3b82f6)}.user-recommendation-card__content .users-list .user-item:nth-child(3) .user-avatar__initials{background:linear-gradient(135deg,#34d399,#10b981)}.user-recommendation-card__content .users-list .user-item:nth-child(4) .user-avatar__initials{background:linear-gradient(135deg,#fbbf24,#f59e0b)}.user-recommendation-card__content .users-list .user-item:nth-child(5) .user-avatar__initials{background:linear-gradient(135deg,#a78bfa,#8b5cf6)}.user-recommendation-card__content .users-list .user-item .user-info{text-align:center;margin-bottom:16px;flex:1;display:flex;flex-direction:column;justify-content:center}.user-recommendation-card__content .users-list .user-item .user-info .user-name{font-size:16px;font-weight:600;color:var(--text-primary, #1f2937);margin:0 0 8px;line-height:1.2}.user-recommendation-card__content .users-list .user-item .user-info .user-stats{display:flex;justify-content:center;margin-top:4px}.user-recommendation-card__content .users-list .user-item .user-info .user-stats .dynamic-count{font-size:13px;color:rgba(var(--text-primary-rgb, 31, 41, 55),.6);font-weight:400}.user-recommendation-card__content .users-list .user-item .follow-btn{background:var(--accent-600, #2563eb);color:#fff;border:none;padding:10px 20px;border-radius:20px;font-size:14px;font-weight:600;cursor:pointer;transition:all .2s ease;flex-shrink:0;width:calc(100% - 32px);max-width:140px}.user-recommendation-card__content .users-list .user-item .follow-btn:hover{background:var(--accent-700, #1d4ed8);transform:translateY(-2px);box-shadow:0 6px 12px #2563eb66}.user-recommendation-card__content .users-list .user-item .follow-btn:active{transform:translateY(0)}@media (max-width: 1024px){.user-recommendation-card__content .users-list .user-item{min-width:160px;max-width:180px;min-height:200px;padding:18px 14px}.user-recommendation-card__content .users-list .user-item .user-avatar{width:70px;height:70px}.user-recommendation-card__content .users-list .user-item .user-avatar__initials{font-size:24px}.user-recommendation-card__content .users-list .user-item .user-info .user-name{font-size:15px}.user-recommendation-card__content .users-list .user-item .user-info .user-stats .dynamic-count{font-size:12px;color:rgba(var(--text-primary-rgb, 31, 41, 55),.6)}.user-recommendation-card__content .users-list .user-item .follow-btn{padding:8px 16px;font-size:13px}}@media (max-width: 768px){.user-recommendation-card{padding:16px;max-width:min(95vw,400px)}.user-recommendation-card__header{margin-bottom:16px}.user-recommendation-card__header .section-title{font-size:16px}.user-recommendation-card__header .view-all-btn{font-size:13px}.user-recommendation-card__content .users-list{gap:12px}.user-recommendation-card__content .users-list .user-item{flex:0 0 calc(50% - 6px);min-width:140px;max-width:160px;min-height:180px;padding:16px 12px}.user-recommendation-card__content .users-list .user-item .user-avatar{width:60px;height:60px}.user-recommendation-card__content .users-list .user-item .user-avatar__initials{font-size:20px}.user-recommendation-card__content .users-list .user-item .user-info{margin-bottom:12px}.user-recommendation-card__content .users-list .user-item .user-info .user-name{font-size:14px}.user-recommendation-card__content .users-list .user-item .user-info .user-stats .dynamic-count{font-size:11px;color:rgba(var(--text-primary-rgb, 31, 41, 55),.6)}.user-recommendation-card__content .users-list .user-item .follow-btn{padding:6px 14px;font-size:12px}}@media (max-width: 480px){.user-recommendation-card{padding:12px;max-width:min(96vw,350px)}.user-recommendation-card__content .users-list{gap:10px}.user-recommendation-card__content .users-list .user-item{flex:0 0 calc(50% - 5px);min-width:120px;max-width:140px;min-height:140px;padding:12px 8px;flex-direction:column;text-align:center;align-items:center}.user-recommendation-card__content .users-list .user-item .close-btn{top:6px;right:6px}.user-recommendation-card__content .users-list .user-item .user-avatar{width:50px;height:50px;margin-bottom:8px}.user-recommendation-card__content .users-list .user-item .user-avatar__initials{font-size:16px}.user-recommendation-card__content .users-list .user-item .user-info{flex:1;margin-bottom:8px;text-align:center}.user-recommendation-card__content .users-list .user-item .user-info .user-name{font-size:13px;margin-bottom:4px}.user-recommendation-card__content .users-list .user-item .user-info .user-stats{justify-content:center}.user-recommendation-card__content .users-list .user-item .user-info .user-stats .dynamic-count{font-size:11px}.user-recommendation-card__content .users-list .user-item .follow-btn{width:calc(100% - 16px);padding:6px 10px;font-size:11px;max-width:100px}}.user-recommendation-card.theme-dark .user-recommendation-card__header .section-title{color:var(--text-primary)}.user-recommendation-card.theme-dark .user-recommendation-card__header .view-all-btn{color:var(--text-secondary)}.user-recommendation-card.theme-dark .user-recommendation-card__header .view-all-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.user-recommendation-card.theme-dark .users-list .user-item{background:var(--bg-hover);border-color:var(--border-color)}.user-recommendation-card.theme-dark .users-list .user-item:hover{background:var(--bg-surface);border-color:var(--accent-500)}.user-recommendation-card.theme-dark .users-list .user-item .close-btn{color:var(--text-tertiary)}.user-recommendation-card.theme-dark .users-list .user-item .close-btn:hover{background:var(--bg-surface);color:var(--text-secondary)}.user-recommendation-card.theme-dark .users-list .user-item .user-avatar{border-color:var(--border-color)}.user-recommendation-card.theme-dark .users-list .user-item .user-info .user-name{color:var(--text-primary)}.user-recommendation-card.theme-dark .users-list .user-item .user-info .user-stats .dynamic-count{color:#f8fafc99}.user-recommendation-card.theme-dark .users-list .user-item .follow-btn{background:var(--accent-600)}.user-recommendation-card.theme-dark .users-list .user-item .follow-btn:hover{background:var(--accent-500)}.dashboard-v2{min-height:100vh;background:var(--bg-primary, #ffffff);position:relative;padding-bottom:80px}.dashboard-v2.theme-dark{background:var(--bg-primary, #1f2937)}.dashboard-v2.theme-dark .development-tools{background:#1f2937cc;border-color:#374151}.dashboard-v2.theme-dark .development-tools h3{color:#e5e7eb;border-bottom-color:#374151}.dashboard-v2.theme-dark .development-tools .dev-tools-container{gap:12px}.dashboard-v2.theme-dark .development-tools .dev-tool-link{background:#374151;color:#e5e7eb;border-color:#4b5563}.dashboard-v2.theme-dark .development-tools .dev-tool-link:hover{background:#4b5563;border-color:#6366f1}.dashboard-v2.theme-dark .development-tools .dev-tool-link .icon{color:#a5b4fc}.dashboard-v2.theme-dark .dashboard-v2__tasks-section{background:#1f2937;border:1px solid #374151}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .section-title{color:#f9fafb}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item{background:#374151;border-color:#4b5563}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item:hover{background:#4b5563;border-color:#6366f1}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item.completed{background:linear-gradient(135deg,#064e3b,#065f46);border-color:#10b981}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item .task-icon{background:#111827;box-shadow:0 2px 8px #0000004d}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item .task-title{color:#f9fafb}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item .task-description{color:#d1d5db}.dashboard-v2.theme-dark .dashboard-v2__tasks-section .task-item .task-status{background:#fbbf24;color:#92400e}.dashboard-v2.theme-dark .dashboard-v2__data-section{background:#1f2937;border:1px solid #374151}.dashboard-v2.theme-dark .dashboard-v2__data-section .section-title{color:#f9fafb}.dashboard-v2.theme-dark .dashboard-v2__data-section .data-card{background:linear-gradient(135deg,#374151,#4b5563);border:1px solid #4b5563}.dashboard-v2.theme-dark .dashboard-v2__data-section .data-card:hover{background:linear-gradient(135deg,#4b5563,#6b7280);border-color:#6366f1}.dashboard-v2.theme-dark .dashboard-v2__data-section .data-card .data-icon{background:#111827;box-shadow:0 2px 8px #0000004d}.dashboard-v2.theme-dark .dashboard-v2__data-section .data-card .data-title{color:#d1d5db}.dashboard-v2.theme-dark .dashboard-v2__data-section .data-card .data-value{color:#f9fafb}.dashboard-v2 .development-tools{position:relative;margin:24px auto;padding:16px;max-width:800px;background:#fffc;-webkit-backdrop-filter:blur(10px);backdrop-filter:blur(10px);border-radius:12px;border:1px solid #e2e8f0;box-shadow:0 4px 6px #0000000d}.dashboard-v2 .development-tools h3{font-size:16px;font-weight:600;margin-bottom:12px;color:#64748b;border-bottom:1px solid #e2e8f0;padding-bottom:8px}.dashboard-v2 .development-tools .dev-tools-container{display:flex;flex-wrap:wrap;gap:10px}.dashboard-v2 .development-tools .dev-tool-link{display:flex;align-items:center;gap:8px;padding:10px 16px;background:#f1f5f9;border-radius:8px;border:1px solid #e2e8f0;color:#334155;text-decoration:none;font-size:14px;font-weight:500;transition:all .2s ease}.dashboard-v2 .development-tools .dev-tool-link:hover{background:#e2e8f0;transform:translateY(-1px);box-shadow:0 2px 5px #0000000d;border-color:#94a3b8}.dashboard-v2 .development-tools .dev-tool-link .icon{color:#64748b}.dashboard-v2__section-title{margin:0 auto 16px;max-width:min(92vw,450px);width:100%;padding:0 20px}.dashboard-v2__section-title h3{font-size:18px;font-weight:700;color:var(--text-primary, #1f2937);margin:0;display:flex;align-items:center;gap:8px}.dashboard-v2__tasks-section{margin:0 4vw 20px;max-width:92vw;background:#fff;border-radius:16px;padding:20px;box-shadow:0 4px 12px #0000001a}.dashboard-v2__tasks-section .section-title{font-size:18px;font-weight:700;color:#1f2937;margin:0 0 16px;display:flex;align-items:center;gap:8px}.dashboard-v2__tasks-section .section-title:before{content:"📋";font-size:20px}.dashboard-v2__tasks-section .tasks-list{display:flex;flex-direction:column;gap:12px}.dashboard-v2__tasks-section .task-item{display:flex;align-items:center;gap:12px;padding:16px;background:#f9fafb;border-radius:12px;border:2px solid transparent;transition:all .3s ease;cursor:pointer}.dashboard-v2__tasks-section .task-item:hover{background:#eef2ff;border-color:#c7d2fe;transform:translateY(-2px);box-shadow:0 4px 12px #4f46e526}.dashboard-v2__tasks-section .task-item.completed{background:linear-gradient(135deg,#dcfce7,#bbf7d0);border-color:#22c55e}.dashboard-v2__tasks-section .task-item.completed .task-title{text-decoration:line-through;color:#6b7280}.dashboard-v2__tasks-section .task-item.completed .task-status{background:#22c55e;color:#fff}.dashboard-v2__tasks-section .task-item .task-icon{font-size:24px;width:40px;height:40px;background:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0000001a;flex-shrink:0}.dashboard-v2__tasks-section .task-item .task-content{flex:1;min-width:0}.dashboard-v2__tasks-section .task-item .task-content .task-title{font-size:16px;font-weight:600;color:#1f2937;margin-bottom:4px}.dashboard-v2__tasks-section .task-item .task-content .task-description{font-size:14px;color:#6b7280;line-height:1.4}.dashboard-v2__tasks-section .task-item .task-status{padding:6px 12px;border-radius:20px;font-size:12px;font-weight:600;background:#fbbf24;color:#92400e;flex-shrink:0}.dashboard-v2__data-section{margin:0 4vw 20px;max-width:92vw;background:#fff;border-radius:16px;padding:20px;box-shadow:0 4px 12px #0000001a}.dashboard-v2__data-section .section-title{font-size:18px;font-weight:700;color:#1f2937;margin:0 0 16px;display:flex;align-items:center;gap:8px}.dashboard-v2__data-section .section-title:before{content:"📊";font-size:20px}.dashboard-v2__data-section .data-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:16px}.dashboard-v2__data-section .data-card{background:linear-gradient(135deg,#f8fafc,#e2e8f0);border-radius:12px;padding:16px;display:flex;align-items:center;gap:12px;transition:all .3s ease;cursor:pointer}.dashboard-v2__data-section .data-card:hover{background:linear-gradient(135deg,#eef2ff,#ddd6fe);transform:translateY(-2px);box-shadow:0 4px 12px #4f46e526}.dashboard-v2__data-section .data-card .data-icon{font-size:24px;width:48px;height:48px;background:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;box-shadow:0 2px 8px #0000001a;flex-shrink:0}.dashboard-v2__data-section .data-card .data-content{flex:1;min-width:0}.dashboard-v2__data-section .data-card .data-content .data-title{font-size:14px;color:#6b7280;margin-bottom:4px;font-weight:500}.dashboard-v2__data-section .data-card .data-content .data-value{font-size:18px;font-weight:700;color:#1f2937}.dashboard-v2__chat-button{position:fixed;bottom:100px;right:20px;background:linear-gradient(135deg,#4f46e5,#7c3aed);color:#fff;border:none;border-radius:50px;padding:12px 20px;display:flex;align-items:center;gap:8px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 8px 24px #4f46e54d;transition:all .3s ease;z-index:1000}.dashboard-v2__chat-button:hover{transform:translateY(-2px);box-shadow:0 12px 32px #4f46e566}.dashboard-v2__chat-button:active{transform:translateY(0)}.dashboard-v2__chat-button .chat-icon{font-size:16px}.dashboard-v2__chat-button .chat-text{white-space:nowrap}@keyframes float{0%,to{transform:translateY(0)}50%{transform:translateY(-10px)}}@media (max-width: 768px){.dashboard-v2__section-title{max-width:min(95vw,400px);padding:0 16px}.dashboard-v2__section-title h3{font-size:16px}.dashboard-v2__tasks-section,.dashboard-v2__data-section{margin:0 3vw 16px;max-width:94vw;padding:16px}.dashboard-v2 .data-grid{grid-template-columns:1fr;gap:12px}.dashboard-v2 .data-card{padding:12px}.dashboard-v2 .data-card .data-icon{width:40px;height:40px;font-size:20px}.dashboard-v2 .data-card .data-value{font-size:16px}.dashboard-v2__chat-button{bottom:80px;right:16px;padding:10px 16px;font-size:13px}.dashboard-v2__chat-button .chat-icon{font-size:14px}}@media (max-width: 480px){.dashboard-v2__section-title{max-width:min(96vw,350px);padding:0 12px}.dashboard-v2__section-title h3{font-size:15px}.dashboard-v2 .task-item{padding:12px}.dashboard-v2 .task-item .task-icon{width:36px;height:36px;font-size:20px}.dashboard-v2 .task-item .task-title{font-size:15px}.dashboard-v2 .task-item .task-description{font-size:13px}}.profile-page{max-width:1200px;margin:0 auto;padding:0}.profile-header{background:linear-gradient(135deg,var(--bg-surface) 0%,var(--bg-card) 100%);border:1px solid var(--primary-500, #334155);border-radius:var(--radius-lg, .5rem);padding:var(--space-8, 2rem);margin-bottom:var(--space-8, 2rem);position:relative;overflow:hidden}.profile-header:before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:linear-gradient(90deg,var(--accent-500, #3b82f6),var(--success-500, #22c55e))}.profile-header-content{display:flex;gap:var(--space-8, 2rem);align-items:flex-start}.profile-avatar-section{position:relative;flex-shrink:0;display:flex;flex-direction:column;align-items:center;gap:var(--space-4, 1rem)}.profile-avatar{width:7.5rem;height:7.5rem;border-radius:var(--radius-full, 9999px);overflow:hidden;border:4px solid var(--accent-500, #3b82f6);position:relative;background:var(--primary-600, #1e293b)}.profile-avatar .avatar-image{width:100%;height:100%;object-fit:cover}.profile-info{text-align:center}.profile-info .profile-name{font-size:var(--text-2xl, 1.5rem);font-weight:var(--font-bold, 700);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-1, .25rem) 0;line-height:1.2}.profile-info .profile-username{font-size:var(--text-base, 1rem);color:var(--text-secondary, #94a3b8);margin:0 0 var(--space-1, .25rem) 0;font-weight:var(--font-medium, 500)}.profile-info .profile-fitness-level{font-size:var(--text-sm, .875rem);color:var(--accent-400, #60a5fa);margin:0;font-weight:var(--font-medium, 500);padding:var(--space-1, .25rem) var(--space-3, .75rem);background-color:var(--primary-600, #1e293b);border-radius:var(--radius-full, 9999px);border:1px solid var(--accent-500, #3b82f6);display:inline-block}.avatar-status{position:absolute;bottom:.5rem;right:.5rem;width:1.25rem;height:1.25rem;border-radius:var(--radius-full, 9999px);border:3px solid var(--bg-surface, #1e293b)}.avatar-status.online{background-color:var(--success-500, #22c55e)}.avatar-status.offline{background-color:var(--text-tertiary, #94a3b8)}.change-avatar-btn{position:absolute;bottom:-.5rem;left:50%;transform:translate(-50%);width:2.5rem;height:2.5rem;border-radius:var(--radius-full, 9999px);background-color:var(--accent-500, #3b82f6);border:2px solid var(--bg-surface, #1e293b);color:var(--text-on-accent, #ffffff);cursor:pointer;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.change-avatar-btn svg{width:1.125rem;height:1.125rem}.change-avatar-btn:hover{background-color:var(--accent-400, #60a5fa);transform:translate(-50%) translateY(-2px)}.profile-info{flex:1;min-width:0}.profile-basic-info{margin-bottom:var(--space-4, 1rem)}.profile-name{font-size:var(--text-3xl, 1.875rem);font-weight:var(--font-bold, 700);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-2, .5rem) 0;font-family:var(--font-display, "Inter", sans-serif)}.profile-username{font-size:var(--text-base, 1rem);color:var(--text-secondary, #cbd5e1);margin:0 0 var(--space-3, .75rem) 0;font-family:var(--font-mono, "SF Mono", monospace)}.profile-meta{display:flex;flex-wrap:wrap;gap:var(--space-4, 1rem);font-size:var(--text-sm, .875rem);color:var(--text-tertiary, #94a3b8)}.profile-level{background-color:var(--success-500, #22c55e);color:var(--text-on-accent, #ffffff);padding:.25rem .75rem;border-radius:var(--radius-full, 9999px);font-weight:var(--font-medium, 500);font-size:var(--text-xs, .75rem)}.profile-bio{color:var(--text-secondary, #cbd5e1);font-size:var(--text-base, 1rem);line-height:1.6;margin:0 0 var(--space-6, 1.5rem) 0}.profile-actions{display:flex;gap:var(--space-3, .75rem);flex-wrap:wrap}.btn-primary{background-color:var(--accent-500, #3b82f6);color:var(--text-on-accent, #ffffff);padding:var(--space-3, .75rem) var(--space-6, 1.5rem);border-radius:var(--radius-md, .375rem);font-weight:var(--font-medium, 500);font-size:var(--text-sm, .875rem);border:none;cursor:pointer;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.btn-primary:hover{background-color:var(--accent-400, #60a5fa);transform:translateY(-1px)}.btn-secondary{background-color:transparent;color:var(--text-secondary, #cbd5e1);border:1px solid var(--primary-500, #334155);padding:var(--space-3, .75rem) var(--space-6, 1.5rem);border-radius:var(--radius-md, .375rem);font-weight:var(--font-medium, 500);font-size:var(--text-sm, .875rem);cursor:pointer;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1));display:flex;align-items:center;gap:var(--space-2, .5rem)}.btn-secondary svg{width:1rem;height:1rem}.btn-secondary:hover{background-color:var(--primary-600, #1e293b);color:var(--text-primary, #f8fafc);border-color:var(--accent-500, #3b82f6)}.btn-text{background:none;border:none;color:var(--accent-500, #3b82f6);font-size:var(--text-sm, .875rem);font-weight:var(--font-medium, 500);cursor:pointer;transition:color var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.btn-text:hover{color:var(--accent-400, #60a5fa)}.section-title{font-size:var(--text-xl, 1.25rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-6, 1.5rem) 0;font-family:var(--font-display, "Inter", sans-serif)}.section-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-6, 1.5rem)}.stats-section{margin-bottom:var(--space-8, 2rem)}.stats-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:var(--space-4, 1rem)}.stat-card{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg, .5rem);padding:var(--space-6, 1.5rem);text-align:center;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.stat-card:hover{border-color:var(--accent-500, #3b82f6);transform:translateY(-2px);box-shadow:0 10px 25px -5px #3b82f61a}.stat-card.highlight{border-color:var(--success-500, #22c55e);background:linear-gradient(135deg,var(--bg-surface, #1e293b) 0%,rgba(34,197,94,.1) 100%)}.stat-icon{font-size:2rem;line-height:1;margin-bottom:var(--space-3, .75rem)}.stat-value{font-size:var(--text-2xl, 1.5rem);font-weight:var(--font-bold, 700);color:var(--text-primary, #f8fafc);font-family:var(--font-mono, "SF Mono", monospace);margin-bottom:var(--space-1, .25rem)}.stat-label{font-size:var(--text-sm, .875rem);color:var(--text-secondary, #cbd5e1);text-transform:uppercase;letter-spacing:.05em;font-weight:var(--font-medium, 500)}.workouts-section{margin-bottom:var(--space-8, 2rem)}.workouts-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(350px,1fr));gap:var(--space-6, 1.5rem)}.workout-card{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg, .5rem);padding:var(--space-6, 1.5rem);transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.workout-card:hover{border-color:var(--accent-500, #3b82f6);transform:translateY(-2px);box-shadow:0 10px 25px -5px #3b82f61a}.workout-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--space-4, 1rem)}.workout-name{font-size:var(--text-lg, 1.125rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);margin:0}.workout-date{font-size:var(--text-sm, .875rem);color:var(--text-tertiary, #94a3b8);font-family:var(--font-mono, "SF Mono", monospace)}.workout-stats{display:flex;gap:var(--space-6, 1.5rem);margin-bottom:var(--space-4, 1rem);padding:var(--space-4, 1rem);background-color:var(--primary-600, #1e293b);border-radius:var(--radius-md, .375rem)}.workout-stat{text-align:center;flex:1}.workout-stat-value{font-size:var(--text-lg, 1.125rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);font-family:var(--font-mono, "SF Mono", monospace)}.workout-stat-label{font-size:var(--text-xs, .75rem);color:var(--text-secondary, #cbd5e1);margin-top:var(--space-1, .25rem)}.workout-exercises{margin-bottom:var(--space-4, 1rem);font-size:var(--text-sm, .875rem)}.workout-exercises .exercises-label{color:var(--text-secondary, #cbd5e1);font-weight:var(--font-medium, 500)}.workout-exercises .exercises-list{color:var(--text-primary, #f8fafc);margin-left:var(--space-2, .5rem)}.workout-view-btn{width:100%;background-color:transparent;color:var(--accent-500, #3b82f6);border:1px solid var(--accent-500, #3b82f6);padding:var(--space-2, .5rem) var(--space-4, 1rem);border-radius:var(--radius-md, .375rem);font-weight:var(--font-medium, 500);font-size:var(--text-sm, .875rem);cursor:pointer;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.workout-view-btn:hover{background-color:var(--accent-500, #3b82f6);color:var(--text-on-accent, #ffffff)}.achievements-section{margin-bottom:var(--space-8, 2rem)}.achievements-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:var(--space-4, 1rem)}.achievement-card{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg, .5rem);padding:var(--space-6, 1.5rem);display:flex;align-items:center;gap:var(--space-4, 1rem);position:relative;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.achievement-card:hover{transform:translateY(-2px);box-shadow:0 10px 25px -5px #0000001a}.achievement-card.achievement-common{border-color:var(--text-tertiary, #94a3b8)}.achievement-card.achievement-rare{border-color:var(--accent-500, #3b82f6);background:linear-gradient(135deg,var(--bg-surface, #1e293b) 0%,rgba(59,130,246,.05) 100%)}.achievement-card.achievement-epic{border-color:var(--warning-500, #f59e0b);background:linear-gradient(135deg,var(--bg-surface, #1e293b) 0%,rgba(245,158,11,.05) 100%)}.achievement-icon{font-size:2.5rem;line-height:1;flex-shrink:0}.achievement-content{flex:1;min-width:0}.achievement-title{font-size:var(--text-base, 1rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-1, .25rem) 0}.achievement-description{font-size:var(--text-sm, .875rem);color:var(--text-secondary, #cbd5e1);margin:0 0 var(--space-2, .5rem) 0;line-height:1.4}.achievement-date{font-size:var(--text-xs, .75rem);color:var(--text-tertiary, #94a3b8);font-family:var(--font-mono, "SF Mono", monospace)}.achievement-rarity{position:absolute;top:var(--space-2, .5rem);right:var(--space-2, .5rem);font-size:var(--text-xs, .75rem);font-weight:var(--font-medium, 500);text-transform:uppercase;letter-spacing:.05em;padding:.125rem .375rem;border-radius:var(--radius-sm, .125rem)}.achievement-common .achievement-rarity{background-color:var(--primary-500, #334155);color:var(--text-secondary, #cbd5e1)}.achievement-rare .achievement-rarity{background-color:var(--accent-500, #3b82f6);color:var(--text-on-accent, #ffffff)}.achievement-epic .achievement-rarity{background-color:var(--warning-500, #f59e0b);color:var(--text-on-accent, #ffffff)}@media (max-width: 768px){.profile-page{padding:0}.profile-header{padding:var(--space-6, 1.5rem);margin-bottom:var(--space-6, 1.5rem)}.profile-header-content{flex-direction:column;text-align:center;gap:var(--space-6, 1.5rem)}.profile-avatar{width:6rem;height:6rem}.profile-name{font-size:var(--text-2xl, 1.5rem)}.profile-meta,.profile-actions{justify-content:center}.stats-grid{grid-template-columns:repeat(2,1fr);gap:var(--space-3, .75rem)}.stat-card{padding:var(--space-4, 1rem)}.stat-value{font-size:var(--text-xl, 1.25rem)}.workouts-grid{grid-template-columns:1fr;gap:var(--space-4, 1rem)}.workout-stats{gap:var(--space-4, 1rem)}.achievements-grid{grid-template-columns:1fr}.achievement-card{padding:var(--space-4, 1rem);gap:var(--space-3, .75rem)}.achievement-icon{font-size:2rem}}@media (min-width: 769px) and (max-width: 1024px){.profile-header-content{gap:var(--space-6, 1.5rem)}.stats-grid{grid-template-columns:repeat(3,1fr)}.workouts-grid{grid-template-columns:repeat(auto-fit,minmax(300px,1fr))}}.loading-skeleton{background:linear-gradient(90deg,var(--primary-600, #1e293b) 25%,var(--primary-500, #334155) 50%,var(--primary-600, #1e293b) 75%);background-size:200% 100%;animation:loading 1.5s infinite}@keyframes loading{0%{background-position:200% 0}to{background-position:-200% 0}}.btn-primary:focus,.btn-secondary:focus,.workout-view-btn:focus,.change-avatar-btn:focus{outline:2px solid var(--accent-500, #3b82f6);outline-offset:2px}@media (prefers-reduced-motion: reduce){.stat-card:hover,.workout-card:hover,.achievement-card:hover{transform:none}.loading-skeleton{animation:none}}.muscle-group:hover .muscle-default{fill:#93c5fd}.fill-transparent{fill:transparent}.loading{fill:#757575;animation:pulse 2s linear infinite}@keyframes pulse{0%{opacity:.5}50%{opacity:.8}to{opacity:.5}}@media (max-width: 768px){#muscle-illustration{max-width:95%}}@media (max-width: 768px){.svgContainer{max-width:95%}}.workout-carousel{position:relative;width:100%;height:100%;overflow:hidden;border-radius:var(--radius-lg);background:var(--bg-surface)}.workout-carousel.empty{display:flex;align-items:center;justify-content:center;min-height:200px}.workout-carousel.empty .empty-state{color:var(--text-tertiary);font-size:var(--text-sm)}.carousel-container{position:relative;width:100%;height:100%;overflow:hidden}.carousel-track{display:flex;width:100%;height:100%;will-change:transform;transform:translateZ(0);backface-visibility:hidden}.carousel-item{flex:0 0 100%;width:100%;height:100%;display:flex;align-items:center;justify-content:center}.carousel-item.active{z-index:1}.carousel-item-content{width:100%;height:100%;display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--space-4)}.muscle-illustration-content .muscle-illustration-wrapper{flex:1;display:flex;align-items:center;justify-content:center;width:100%;max-height:300px}.muscle-illustration-content .muscle-illustration-wrapper .carousel-muscle-illustration{max-width:100%;max-height:100%;object-fit:contain}.muscle-illustration-content .muscle-stats{display:flex;gap:var(--space-6);margin-top:var(--space-4);padding:var(--space-3) var(--space-4);background:var(--bg-secondary);border-radius:var(--radius-md)}.muscle-illustration-content .muscle-stats .muscle-count,.muscle-illustration-content .muscle-stats .intensity-level{display:flex;flex-direction:column;align-items:center;gap:var(--space-1)}.muscle-illustration-content .muscle-stats .muscle-count .count,.muscle-illustration-content .muscle-stats .muscle-count .level,.muscle-illustration-content .muscle-stats .intensity-level .count,.muscle-illustration-content .muscle-stats .intensity-level .level{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--accent-500)}.muscle-illustration-content .muscle-stats .muscle-count .label,.muscle-illustration-content .muscle-stats .intensity-level .label{font-size:var(--text-xs);color:var(--text-secondary)}.user-image-content .user-image{width:100%;height:auto;max-height:300px;object-fit:cover;border-radius:var(--radius-md);box-shadow:var(--shadow-md)}.user-image-content .image-caption{margin-top:var(--space-3);padding:var(--space-2) var(--space-3);background:var(--bg-secondary);border-radius:var(--radius-sm);font-size:var(--text-sm);color:var(--text-secondary);text-align:center}.carousel-nav{position:absolute;top:50%;transform:translateY(-50%);width:var(--ios-touch-target);height:var(--ios-touch-target);border:none;border-radius:50%;background:#ffffffe6;color:var(--text-primary);display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);box-shadow:var(--shadow-md);z-index:2;-webkit-tap-highlight-color:transparent}.carousel-nav:hover{background:#fff;transform:translateY(-50%) scale(1.05)}.carousel-nav:active{transform:translateY(-50%) scale(.95)}.carousel-nav:disabled{opacity:.5;cursor:not-allowed;transform:translateY(-50%)}.carousel-nav.prev{left:var(--space-3)}.carousel-nav.next{right:var(--space-3)}.carousel-nav svg{width:20px;height:20px}.carousel-indicators{position:absolute;bottom:var(--space-3);left:50%;transform:translate(-50%);display:flex;gap:var(--space-2);z-index:2}.carousel-indicators .indicator{width:8px;height:8px;border:none;border-radius:50%;background:#ffffff80;cursor:pointer;transition:all var(--transition-fast) var(--ease-in-out);-webkit-tap-highlight-color:transparent;min-width:var(--ios-touch-target);min-height:var(--ios-touch-target);display:flex;align-items:center;justify-content:center}.carousel-indicators .indicator:before{content:"";width:8px;height:8px;border-radius:50%;background:currentColor;transition:all var(--transition-fast) var(--ease-in-out)}.carousel-indicators .indicator.active{background:#ffffffe6}.carousel-indicators .indicator.active:before{background:var(--accent-500);transform:scale(1.2)}.carousel-indicators .indicator:hover:not(.active){background:#ffffffb3}.theme-dark .carousel-nav{background:#1e293be6;color:var(--text-primary)}.theme-dark .carousel-nav:hover{background:#1e293b}.theme-dark .carousel-indicators .indicator{background:#1e293b80}.theme-dark .carousel-indicators .indicator.active{background:#1e293be6}.theme-dark .carousel-indicators .indicator:hover:not(.active){background:#1e293bb3}@media (max-width: 768px){.carousel-nav{width:36px;height:36px}.carousel-nav svg{width:16px;height:16px}.carousel-nav.prev{left:var(--space-2)}.carousel-nav.next{right:var(--space-2)}.carousel-indicators{bottom:var(--space-2)}.carousel-indicators .indicator{width:6px;height:6px}.carousel-indicators .indicator:before{width:6px;height:6px}.muscle-illustration-content .muscle-illustration-wrapper{max-height:250px}.muscle-illustration-content .muscle-stats{gap:var(--space-4);margin-top:var(--space-3);padding:var(--space-2) var(--space-3)}.user-image-content .user-image{max-height:250px}}@media (prefers-reduced-motion: reduce){.carousel-track{transition:none!important}.carousel-nav,.carousel-indicators .indicator{transition:none}.carousel-nav:hover,.carousel-nav:active{transform:translateY(-50%)}}.exercise-list{display:flex;flex-direction:column;gap:var(--space-3)}.exercise-list.empty{display:flex;align-items:center;justify-content:center;min-height:120px}.exercise-list.empty .empty-state{display:flex;flex-direction:column;align-items:center;gap:var(--space-2);color:var(--text-tertiary)}.exercise-list.empty .empty-state svg{opacity:.5}.exercise-list.empty .empty-state span{font-size:var(--text-sm)}.exercise-items{display:flex;flex-direction:column;gap:var(--space-2)}.exercise-item{display:flex;align-items:center;gap:var(--space-3);padding:var(--space-3);background:var(--bg-secondary);border-radius:var(--radius-md);transition:all var(--transition-normal) var(--ease-in-out);position:relative}.exercise-item:hover{background:var(--bg-hover);transform:translateY(-1px);box-shadow:var(--shadow-sm)}.exercise-image{flex-shrink:0;width:48px;height:48px;border-radius:var(--radius-md);overflow:hidden;background:var(--bg-tertiary);display:flex;align-items:center;justify-content:center}.exercise-image img{width:100%;height:100%;object-fit:cover}.exercise-image .exercise-icon{color:var(--text-tertiary)}.exercise-image .exercise-icon.hidden{display:none}.exercise-image .exercise-icon svg{width:24px;height:24px}.exercise-info{flex:1;display:flex;flex-direction:column;gap:var(--space-1);min-width:0}.exercise-info .exercise-name{font-size:var(--text-base);font-weight:var(--font-medium);color:var(--text-primary);line-height:1.4;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.exercise-info .exercise-details{display:flex;align-items:center;gap:var(--space-2);font-size:var(--text-sm);color:var(--text-secondary)}.exercise-info .exercise-details .sets-info{font-weight:var(--font-medium);color:var(--accent-500)}.exercise-info .exercise-details .reps-info,.exercise-info .exercise-details .weight-info{color:var(--text-tertiary)}.exercise-info .muscle-groups{font-size:var(--text-xs);color:var(--text-tertiary);line-height:1.3;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.exercise-index{flex-shrink:0;width:24px;height:24px;border-radius:50%;background:var(--accent-500);color:#fff;display:flex;align-items:center;justify-content:center;font-size:var(--text-xs);font-weight:var(--font-medium)}.view-more-section{display:flex;justify-content:center;padding-top:var(--space-2);border-top:1px solid var(--border-light)}.view-more-section .view-more-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-2) var(--space-4);background:transparent;border:1px solid var(--border-color);border-radius:var(--radius-full);color:var(--accent-500);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);-webkit-tap-highlight-color:transparent;min-height:var(--ios-touch-target)}.view-more-section .view-more-btn:hover{background:var(--accent-500);color:#fff;border-color:var(--accent-500);transform:translateY(-1px);box-shadow:var(--shadow-sm)}.view-more-section .view-more-btn:active{transform:translateY(0)}.view-more-section .view-more-btn .btn-icon{transition:transform var(--transition-normal) var(--ease-in-out)}.view-more-section .view-more-btn .btn-icon.rotated{transform:rotate(180deg)}.exercise-summary{display:flex;justify-content:space-around;padding:var(--space-3);background:var(--bg-tertiary);border-radius:var(--radius-md);margin-top:var(--space-2)}.exercise-summary .summary-item{display:flex;flex-direction:column;align-items:center;gap:var(--space-1)}.exercise-summary .summary-item .summary-value{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--accent-500);line-height:1}.exercise-summary .summary-item .summary-label{font-size:var(--text-xs);color:var(--text-secondary);line-height:1}@media (max-width: 768px){.exercise-item{padding:var(--space-2);gap:var(--space-2)}.exercise-image{width:40px;height:40px}.exercise-image .exercise-icon svg{width:20px;height:20px}.exercise-info .exercise-name{font-size:var(--text-sm)}.exercise-info .exercise-details{font-size:var(--text-xs);gap:var(--space-1)}.exercise-info .muscle-groups{font-size:var(--text-xs)}.exercise-index{width:20px;height:20px;font-size:var(--text-xs)}.exercise-summary{padding:var(--space-2)}.exercise-summary .summary-item .summary-value{font-size:var(--text-base)}.exercise-summary .summary-item .summary-label{font-size:var(--text-xs)}}.theme-dark .exercise-item{background:var(--bg-secondary)}.theme-dark .exercise-item:hover{background:var(--bg-hover)}.theme-dark .exercise-image{background:var(--bg-tertiary)}.theme-dark .view-more-section .view-more-btn{border-color:var(--border-color)}.theme-dark .view-more-section .view-more-btn:hover{background:var(--accent-500);border-color:var(--accent-500)}.theme-dark .exercise-summary{background:var(--bg-secondary)}@media (prefers-reduced-motion: reduce){.exercise-item,.view-more-section .view-more-btn,.view-more-section .btn-icon{transition:none}.exercise-item:hover,.view-more-section .view-more-btn:hover,.view-more-section .view-more-btn:active{transform:none}}@media (prefers-contrast: high){.exercise-item{border:1px solid var(--border-color)}.view-more-section .view-more-btn{border-width:2px}.exercise-summary{border:1px solid var(--border-color)}}.feed-page{max-width:600px;margin:0 auto;padding:0}.feed-header{margin-bottom:var(--space-6)}.feed-header .feed-title{margin-bottom:var(--space-4)}.feed-header .feed-title h1{font-size:var(--text-2xl);font-weight:var(--font-bold);color:var(--text-primary);margin:0 0 var(--space-2) 0}.feed-header .feed-title p{color:var(--text-secondary);font-size:var(--text-base);margin:0}.feed-header .feed-filters{display:flex;gap:var(--space-2);padding:var(--space-1);background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-lg)}.feed-header .feed-filters .filter-btn{flex:1;padding:var(--space-2) var(--space-4);border:none;border-radius:var(--radius-md);background:transparent;color:var(--text-secondary);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.feed-header .feed-filters .filter-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.feed-header .feed-filters .filter-btn.active{background:var(--accent-500);color:var(--text-on-accent)}.create-post-card{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg);padding:var(--space-4);margin-bottom:var(--space-6)}.create-post-card .create-post-header{display:flex;align-items:center;gap:var(--space-3);margin-bottom:var(--space-4)}.create-post-card .create-post-header .user-avatar{width:2.5rem;height:2.5rem;border-radius:var(--radius-full);overflow:hidden;border:2px solid var(--accent-500)}.create-post-card .create-post-header .user-avatar img{width:100%;height:100%;object-fit:cover}.create-post-card .create-post-header .create-post-input{flex:1;padding:var(--space-3) var(--space-4);background:var(--primary-600);border:1px solid var(--primary-500);border-radius:var(--radius-full);color:var(--text-secondary);font-size:var(--text-base);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);text-align:left}.create-post-card .create-post-header .create-post-input:hover{background:var(--primary-500);border-color:var(--accent-500)}.create-post-card .create-post-actions{display:flex;gap:var(--space-3)}.create-post-card .create-post-actions .post-action-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-2) var(--space-3);background:transparent;border:1px solid var(--primary-500);border-radius:var(--radius-md);color:var(--text-secondary);font-size:var(--text-sm);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.create-post-card .create-post-actions .post-action-btn svg{width:1rem;height:1rem;stroke-width:2}.create-post-card .create-post-actions .post-action-btn:hover{background:var(--primary-600);color:var(--text-primary);border-color:var(--accent-500)}.feed-posts{display:flex;flex-direction:column;gap:var(--space-6)}.feed-post{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg);padding:var(--space-6);transition:all var(--transition-normal) var(--ease-in-out)}.feed-post:hover{border-color:var(--accent-500);transform:translateY(-2px);box-shadow:0 10px 25px -5px #3b82f61a}.post-header{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--space-4)}.post-header .post-user{display:flex;align-items:center;gap:var(--space-3)}.post-header .post-user .user-avatar{width:2.5rem;height:2.5rem;border-radius:var(--radius-full);overflow:hidden;border:2px solid var(--accent-500)}.post-header .post-user .user-avatar img{width:100%;height:100%;object-fit:cover}.post-header .post-user .user-info .user-name{display:flex;align-items:center;gap:var(--space-1);font-size:var(--text-base);font-weight:var(--font-semibold);color:var(--text-primary);margin-bottom:var(--space-1)}.post-header .post-user .user-info .user-name .verified-icon{width:1rem;height:1rem;color:var(--accent-500)}.post-header .post-user .user-info .post-meta{display:flex;align-items:center;gap:var(--space-2);font-size:var(--text-sm);color:var(--text-tertiary)}.post-header .post-user .user-info .post-meta .username{color:var(--accent-500)}.post-header .post-user .user-info .post-meta .location{display:flex;align-items:center;gap:var(--space-1)}.post-header .post-user .user-info .post-meta .location svg{width:.875rem;height:.875rem;stroke-width:2}.post-header .post-menu-btn{padding:var(--space-1);background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.post-header .post-menu-btn svg{width:1.25rem;height:1.25rem;stroke-width:2}.post-header .post-menu-btn:hover{background:var(--primary-600);color:var(--text-primary)}.post-content{margin-bottom:var(--space-4)}.post-content .post-text{font-size:var(--text-base);line-height:var(--leading-relaxed);color:var(--text-primary);margin:0 0 var(--space-4) 0}.post-content .post-image{margin-bottom:var(--space-4);border-radius:var(--radius-lg);overflow:hidden}.post-content .post-image img{width:100%;height:auto;display:block}.workout-summary{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg);padding:var(--space-4);margin-bottom:var(--space-4)}.workout-summary .workout-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-3)}.workout-summary .workout-header .workout-name{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.workout-summary .workout-header .workout-badge{background:var(--accent-500);color:var(--text-on-accent);font-size:var(--text-xs);font-weight:var(--font-medium);padding:.25rem .75rem;border-radius:var(--radius-full);text-transform:uppercase;letter-spacing:.05em}.workout-summary .workout-stats{display:grid;grid-template-columns:repeat(3,1fr);gap:var(--space-4);margin-bottom:var(--space-4)}.workout-summary .workout-stats .workout-stat{text-align:center}.workout-summary .workout-stats .workout-stat .stat-value{display:block;font-size:var(--text-xl);font-weight:var(--font-bold);color:var(--text-primary);font-family:var(--font-mono);margin-bottom:var(--space-1)}.workout-summary .workout-stats .workout-stat .stat-label{font-size:var(--text-xs);color:var(--text-secondary);text-transform:uppercase;letter-spacing:.05em;font-weight:var(--font-medium)}.workout-summary .workout-exercises{font-size:var(--text-sm)}.workout-summary .workout-exercises .exercises-label{color:var(--text-secondary);font-weight:var(--font-medium)}.workout-summary .workout-exercises .exercises-list{color:var(--text-primary);margin-left:var(--space-2)}.post-actions{display:flex;justify-content:space-around;padding-top:var(--space-4);border-top:1px solid var(--primary-500)}.post-actions .action-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-2) var(--space-3);background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-secondary);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.post-actions .action-btn svg{width:1.125rem;height:1.125rem;stroke-width:2}.post-actions .action-btn:hover{background:var(--primary-600);color:var(--text-primary);transform:translateY(-1px)}.post-actions .action-btn.like-btn.liked{color:var(--error-500)}.post-actions .action-btn.like-btn.liked svg{fill:currentColor}.post-actions .action-btn.like-btn:hover{color:var(--error-500)}.post-actions .action-btn.comment-btn:hover{color:var(--accent-500)}.post-actions .action-btn.share-btn:hover{color:var(--success-500)}.load-more{display:flex;justify-content:center;margin:var(--space-8) 0}.load-more .load-more-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-3) var(--space-6);background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-lg);color:var(--text-secondary);font-size:var(--text-base);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.load-more .load-more-btn svg{width:1.25rem;height:1.25rem;stroke-width:2}.load-more .load-more-btn:hover{background:var(--primary-600);color:var(--text-primary);border-color:var(--accent-500);transform:translateY(-2px)}@media (max-width: 768px){.feed-page{padding:0 var(--space-4)}.feed-header .feed-title h1{font-size:var(--text-xl)}.feed-header .feed-filters .filter-btn{padding:var(--space-2);font-size:var(--text-xs)}.create-post-card,.feed-post{padding:var(--space-4)}.post-header .post-user .user-avatar{width:2rem;height:2rem}.post-header .post-user .user-info .user-name{font-size:var(--text-sm)}.workout-summary .workout-stats{grid-template-columns:repeat(3,1fr);gap:var(--space-2)}.workout-summary .workout-stats .workout-stat .stat-value{font-size:var(--text-lg)}.post-actions .action-btn{padding:var(--space-2);font-size:var(--text-xs)}.post-actions .action-btn svg{width:1rem;height:1rem}.create-post-actions{flex-direction:column}.create-post-actions .post-action-btn{justify-content:center}}.feed-post.loading .post-content,.feed-post.loading .post-actions{opacity:.7;pointer-events:none}@keyframes slideInFromTop{0%{opacity:0;transform:translateY(-1rem)}to{opacity:1;transform:translateY(0)}}.feed-post.new-post{animation:slideInFromTop .3s ease-out}.filter-btn:focus,.post-action-btn:focus,.action-btn:focus,.load-more-btn:focus,.create-post-input:focus{outline:2px solid var(--accent-500);outline-offset:2px}@media (prefers-reduced-motion: reduce){.feed-post,.action-btn,.load-more-btn{transition:none}.feed-post:hover,.action-btn:hover,.load-more-btn:hover{transform:none}.feed-post.new-post{animation:none}}@media (prefers-contrast: high){.feed-post,.create-post-card,.workout-summary,.workout-post-content{border-width:2px}}.workout-post-content{background:var(--card-bg);border:1px solid var(--card-border);border-radius:var(--radius-lg);padding:var(--space-4);margin-top:var(--space-4)}.workout-post-content .workout-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-4)}.workout-post-content .workout-header .workout-name{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.workout-post-content .workout-header .workout-badge{background:var(--accent-500);color:var(--text-on-accent);padding:var(--space-1) var(--space-3);border-radius:var(--radius-full);font-size:var(--text-xs);font-weight:var(--font-medium);text-transform:uppercase;letter-spacing:.05em}.workout-post-content .workout-stats-summary{display:grid;grid-template-columns:repeat(4,1fr);gap:var(--space-3);margin-bottom:var(--space-4);padding:var(--space-3);background:var(--bg-tertiary);border-radius:var(--radius-md)}.workout-post-content .workout-stats-summary .workout-stat{text-align:center}.workout-post-content .workout-stats-summary .workout-stat .stat-value{display:block;font-size:var(--text-lg);font-weight:var(--font-bold);color:var(--accent-500);line-height:1.2;font-family:var(--font-mono)}.workout-post-content .workout-stats-summary .workout-stat .stat-label{font-size:var(--text-xs);color:var(--text-secondary);margin-top:var(--space-1);text-transform:uppercase;letter-spacing:.05em;font-weight:var(--font-medium)}.workout-post-content .workout-main-content{display:flex;gap:var(--space-4);align-items:flex-start}.workout-post-content .workout-main-content .workout-exercises-section{flex:0 0 40%;min-width:0}.workout-post-content .workout-main-content .workout-exercises-section .workout-exercise-list{background:transparent}.workout-post-content .workout-main-content .workout-exercises-section .workout-exercise-list .exercise-summary{background:var(--bg-primary);border:1px solid var(--border-light)}.workout-post-content .workout-main-content .workout-carousel-section{flex:1;min-height:300px}.workout-post-content .workout-main-content .workout-carousel-section .workout-post-carousel{height:100%;border-radius:var(--radius-md);overflow:hidden;box-shadow:var(--shadow-sm);background:var(--bg-primary)}@media (max-width: 768px){.workout-post-content{padding:var(--space-3)}.workout-post-content .workout-stats-summary{grid-template-columns:repeat(2,1fr);gap:var(--space-2);padding:var(--space-2)}.workout-post-content .workout-stats-summary .workout-stat .stat-value{font-size:var(--text-base)}.workout-post-content .workout-main-content{flex-direction:column;gap:var(--space-3)}.workout-post-content .workout-main-content .workout-exercises-section{flex:none;width:100%}.workout-post-content .workout-main-content .workout-carousel-section{flex:none;width:100%;min-height:250px}}@media (max-width: 480px){.workout-post-content .workout-stats-summary{grid-template-columns:repeat(2,1fr)}.workout-post-content .workout-stats-summary .workout-stat .stat-value{font-size:var(--text-sm)}.workout-post-content .workout-stats-summary .workout-stat .stat-label{font-size:var(--text-xs)}.workout-post-content .workout-carousel-section{min-height:200px}}@media (min-width: 768px) and (max-width: 1024px) and (orientation: landscape){.workout-post-content .workout-main-content .workout-exercises-section{flex:0 0 35%}.workout-post-content .workout-main-content .workout-carousel-section{flex:1;min-height:280px}}.routines-page{padding:0}.routines-header{margin-bottom:var(--space-8)}.routines-header .header-content{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--space-6)}.routines-header .header-content .title-section h1{font-size:var(--text-3xl);font-weight:var(--font-bold);color:var(--text-primary);margin:0 0 var(--space-2) 0;background:var(--gradient-brand);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.routines-header .header-content .title-section p{color:var(--text-secondary);font-size:var(--text-base);margin:0}.routines-header .header-content .create-routine-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-3) var(--space-6);background:var(--gradient-primary);border:none;border-radius:var(--radius-lg);color:var(--text-on-accent);font-size:var(--text-base);font-weight:var(--font-semibold);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);box-shadow:var(--shadow-md)}.routines-header .header-content .create-routine-btn svg{width:1.25rem;height:1.25rem;stroke-width:2}.routines-header .header-content .create-routine-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg)}.routines-header .header-content .create-routine-btn:active{transform:translateY(0)}.routines-header .controls-section{display:flex;justify-content:space-between;align-items:center;gap:var(--space-4)}.routines-header .controls-section .filter-tabs{display:flex;gap:var(--space-2);padding:var(--space-1);background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-lg)}.routines-header .controls-section .filter-tabs .filter-btn{display:flex;align-items:center;gap:var(--space-2);padding:var(--space-2) var(--space-4);border:none;border-radius:var(--radius-md);background:transparent;color:var(--text-secondary);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);white-space:nowrap}.routines-header .controls-section .filter-tabs .filter-btn svg{width:1rem;height:1rem;stroke-width:2}.routines-header .controls-section .filter-tabs .filter-btn:hover{background:var(--bg-hover);color:var(--text-primary);border-color:var(--accent-500)}.routines-header .controls-section .filter-tabs .filter-btn.active{background:var(--accent-500);color:var(--text-on-accent)}.routines-header .controls-section .sort-dropdown .sort-select{padding:var(--space-2) var(--space-4);background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-md);color:var(--text-secondary);font-size:var(--text-sm);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.routines-header .controls-section .sort-dropdown .sort-select:hover{border-color:var(--accent-500)}.routines-header .controls-section .sort-dropdown .sort-select:focus{outline:2px solid var(--accent-500);outline-offset:2px}.routines-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(320px,1fr));gap:var(--space-6);margin-bottom:var(--space-8)}@media (max-width: 1024px){.routines-grid{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));gap:var(--space-5)}}@media (max-width: 768px){.routines-grid{grid-template-columns:1fr;gap:var(--space-4)}}@media (max-width: 480px){.routines-grid{gap:var(--space-3)}}.routine-card{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-xl);padding:var(--space-6);transition:all var(--transition-normal) var(--ease-in-out);position:relative;overflow:hidden}.routine-card:before{content:"";position:absolute;top:0;left:0;right:0;height:4px;background:var(--gradient-brand);opacity:0;transition:opacity var(--transition-normal) var(--ease-in-out)}.routine-card:hover{border-color:var(--accent-500);transform:translateY(-4px);box-shadow:var(--shadow-xl)}.routine-card:hover:before{opacity:1}.routine-card .card-header{margin-bottom:var(--space-4)}.routine-card .card-header .routine-title{display:flex;justify-content:space-between;align-items:flex-start;margin-bottom:var(--space-2)}.routine-card .card-header .routine-title h3{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0;line-height:var(--leading-tight)}.routine-card .card-header .routine-title .favorite-btn{padding:var(--space-1);background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.routine-card .card-header .routine-title .favorite-btn svg{width:1.25rem;height:1.25rem;stroke-width:2}.routine-card .card-header .routine-title .favorite-btn:hover{color:var(--error-500);background:var(--primary-600)}.routine-card .card-header .routine-title .favorite-btn.favorited{color:var(--error-500)}.routine-card .card-header .routine-description{font-size:var(--text-sm);line-height:var(--leading-relaxed);color:var(--text-secondary);margin:0}.routine-card .card-stats{display:grid;grid-template-columns:repeat(3,1fr);gap:var(--space-4);margin-bottom:var(--space-4);padding:var(--space-4);background:var(--primary-600);border-radius:var(--radius-lg)}.routine-card .card-stats .stat-item{text-align:center}.routine-card .card-stats .stat-item .stat-value{display:block;font-size:var(--text-xl);font-weight:var(--font-bold);color:var(--text-primary);font-family:var(--font-mono);margin-bottom:var(--space-1)}.routine-card .card-stats .stat-item .stat-label{font-size:var(--text-xs);color:var(--text-secondary);text-transform:uppercase;letter-spacing:.05em;font-weight:var(--font-medium)}.routine-card .muscle-groups{display:flex;flex-wrap:wrap;gap:var(--space-2);margin-bottom:var(--space-4)}.routine-card .muscle-groups .muscle-tag{background:var(--accent-500);color:var(--text-on-accent);font-size:var(--text-xs);font-weight:var(--font-medium);padding:.25rem .5rem;border-radius:var(--radius-full);text-transform:uppercase;letter-spacing:.05em}.routine-card .card-meta{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-4);font-size:var(--text-sm)}.routine-card .card-meta .difficulty-badge{font-weight:var(--font-semibold);text-transform:uppercase;letter-spacing:.05em}.routine-card .card-meta .last-used{color:var(--text-tertiary)}.routine-card .card-actions{display:grid;grid-template-columns:1fr 1fr;gap:var(--space-3)}@media (max-width: 480px){.routine-card .card-actions{grid-template-columns:1fr;gap:var(--space-2)}}.routine-card .card-actions .action-btn{display:flex;align-items:center;justify-content:center;gap:var(--space-2);padding:var(--space-3);border:1px solid var(--primary-500);border-radius:var(--radius-md);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.routine-card .card-actions .action-btn svg{width:1rem;height:1rem;stroke-width:2}.routine-card .card-actions .action-btn.secondary{background:transparent;color:var(--text-secondary)}.routine-card .card-actions .action-btn.secondary:hover{background:var(--primary-600);color:var(--text-primary);border-color:var(--accent-500)}.routine-card .card-actions .action-btn.primary{background:var(--accent-500);color:var(--text-on-accent);border-color:var(--accent-500)}.routine-card .card-actions .action-btn.primary:hover{background:var(--accent-400);transform:translateY(-1px)}.empty-state p{color:var(--text-secondary);margin:0 0 var(--space-6) 0;max-width:24rem}.empty-state .create-first-routine-btn{padding:var(--space-3) var(--space-6);background:var(--gradient-primary);border:none;border-radius:var(--radius-lg);color:var(--text-on-accent);font-size:var(--text-base);font-weight:var(--font-semibold);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.empty-state .create-first-routine-btn:hover{transform:translateY(-2px);box-shadow:var(--shadow-lg)}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:#00000080;display:flex;align-items:center;justify-content:center;z-index:var(--z-modal);padding:var(--space-4);-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.routine-detail-modal{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-xl);width:100%;max-width:600px;max-height:90vh;overflow:hidden;display:flex;flex-direction:column;box-shadow:var(--shadow-2xl)}.routine-detail-modal .modal-header{display:flex;justify-content:space-between;align-items:center;padding:var(--space-6);border-bottom:1px solid var(--primary-500)}.routine-detail-modal .modal-header h2{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.routine-detail-modal .modal-header .close-btn{padding:var(--space-2);background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.routine-detail-modal .modal-header .close-btn svg{width:1.25rem;height:1.25rem;stroke-width:2}.routine-detail-modal .modal-header .close-btn:hover{background:var(--primary-600);color:var(--text-primary)}.routine-detail-modal .modal-content{flex:1;overflow-y:auto;padding:var(--space-6)}.routine-detail-modal .modal-content .routine-description{font-size:var(--text-base);line-height:var(--leading-relaxed);color:var(--text-secondary);margin:0 0 var(--space-6) 0}.routine-detail-modal .modal-content .routine-info{display:flex;flex-direction:column;gap:var(--space-3);margin-bottom:var(--space-6);padding:var(--space-4);background:var(--primary-600);border-radius:var(--radius-lg)}.routine-detail-modal .modal-content .routine-info .info-item{display:flex;justify-content:space-between;align-items:center}.routine-detail-modal .modal-content .routine-info .info-item .label{font-weight:var(--font-medium);color:var(--text-secondary)}.routine-detail-modal .modal-content .routine-info .info-item .value{color:var(--text-primary)}.routine-detail-modal .modal-content .routine-info .info-item .value.difficulty{font-weight:var(--font-semibold);text-transform:uppercase;letter-spacing:.05em}.routine-detail-modal .modal-content .exercises-list h3{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0 0 var(--space-4) 0}.routine-detail-modal .modal-content .exercises-list .exercise-item{display:flex;gap:var(--space-4);padding:var(--space-4);background:var(--primary-600);border-radius:var(--radius-lg);margin-bottom:var(--space-3)}.routine-detail-modal .modal-content .exercises-list .exercise-item:last-child{margin-bottom:0}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-number{width:2rem;height:2rem;background:var(--accent-500);color:var(--text-on-accent);border-radius:var(--radius-full);display:flex;align-items:center;justify-content:center;font-weight:var(--font-bold);font-size:var(--text-sm);flex-shrink:0}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-info{flex:1}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-info h4{font-size:var(--text-base);font-weight:var(--font-semibold);color:var(--text-primary);margin:0 0 var(--space-2) 0}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-info .exercise-details{display:flex;flex-wrap:wrap;gap:var(--space-3);font-size:var(--text-sm);color:var(--text-secondary)}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-info .exercise-details span{background:var(--bg-surface);padding:.25rem .5rem;border-radius:var(--radius-md);border:1px solid var(--primary-500)}.routine-detail-modal .modal-actions{display:flex;gap:var(--space-3);padding:var(--space-6);border-top:1px solid var(--primary-500)}.routine-detail-modal .modal-actions .action-btn{flex:1;display:flex;align-items:center;justify-content:center;gap:var(--space-2);padding:var(--space-3);border:1px solid var(--primary-500);border-radius:var(--radius-md);font-size:var(--text-base);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.routine-detail-modal .modal-actions .action-btn.secondary{background:transparent;color:var(--text-secondary)}.routine-detail-modal .modal-actions .action-btn.secondary:hover{background:var(--primary-600);color:var(--text-primary)}.routine-detail-modal .modal-actions .action-btn.primary{background:var(--accent-500);color:var(--text-on-accent);border-color:var(--accent-500)}.routine-detail-modal .modal-actions .action-btn.primary:hover{background:var(--accent-400)}@media (max-width: 768px){.routines-page{padding:0 var(--space-4)}.routines-header .header-content{flex-direction:column;gap:var(--space-4);align-items:stretch}.routines-header .header-content .create-routine-btn{justify-content:center}.routines-header .controls-section{flex-direction:column;gap:var(--space-4)}.routines-header .controls-section .filter-tabs .filter-btn{flex:1;justify-content:center;padding:var(--space-2);font-size:var(--text-xs)}.routines-header .controls-section .filter-tabs .filter-btn svg{width:.875rem;height:.875rem}.routines-header .controls-section .sort-dropdown{align-self:stretch}.routines-header .controls-section .sort-dropdown .sort-select{width:100%}.routines-grid{grid-template-columns:1fr;gap:var(--space-4)}.routine-card{padding:var(--space-4)}.routine-card .card-stats{gap:var(--space-2);padding:var(--space-3)}.routine-card .muscle-groups .muscle-tag{font-size:.6875rem}.routine-card .card-actions{grid-template-columns:1fr;gap:var(--space-2)}.routine-detail-modal{margin:var(--space-4);max-height:calc(100vh - 2rem)}.routine-detail-modal .modal-content{padding:var(--space-4)}.routine-detail-modal .modal-content .exercises-list .exercise-item{flex-direction:column;gap:var(--space-3)}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-number{align-self:flex-start}.routine-detail-modal .modal-content .exercises-list .exercise-item .exercise-details{gap:var(--space-2)}.routine-detail-modal .modal-actions{padding:var(--space-4);flex-direction:column}}@keyframes slideUp{0%{opacity:0;transform:translateY(1rem)}to{opacity:1;transform:translateY(0)}}.routine-card{animation:slideUp .3s ease-out}.routine-detail-modal{animation:slideUp .2s ease-out}.filter-btn:focus,.sort-select:focus,.action-btn:focus,.favorite-btn:focus,.close-btn:focus{outline:2px solid var(--accent-500);outline-offset:2px}@media (prefers-reduced-motion: reduce){.routine-card,.routine-detail-modal,.action-btn,.create-routine-btn{animation:none;transition:none}.routine-card:hover,.action-btn:hover,.create-routine-btn:hover{transform:none}}@media (prefers-contrast: high){.routine-card,.routine-detail-modal{border-width:2px}.card-stats{border:1px solid var(--primary-400)}}.exercises-page{padding:0;min-height:100vh;background:var(--bg-primary);max-width:100vw;width:100%;margin:0 auto;overflow-x:hidden}.exercises-header{background:var(--bg-surface);border-bottom:1px solid var(--border-color);margin-bottom:var(--space-4)}.exercises-header .header-top{display:flex;justify-content:space-between;align-items:flex-start;padding:var(--space-4) var(--space-4) var(--space-3) var(--space-4);padding-top:calc(env(safe-area-inset-top) + var(--space-4))}.exercises-header .header-top .title-section{flex:1}.exercises-header .header-top .title-section h1{font-size:var(--text-3xl);font-weight:var(--font-bold);color:var(--text-primary);margin:0 0 var(--space-1) 0;background:var(--gradient-brand);-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.exercises-header .header-top .title-section p{color:var(--text-secondary);font-size:var(--text-base);margin:0}.exercises-header .header-top .header-actions{display:flex;gap:var(--space-2)}.exercises-header .header-top .header-actions .action-icon-btn{width:44px;height:44px;border-radius:var(--radius-full);background:var(--accent-500);border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.exercises-header .header-top .header-actions .action-icon-btn svg{width:20px;height:20px;color:var(--text-on-accent);stroke-width:2}.exercises-header .header-top .header-actions .action-icon-btn:hover{background:var(--accent-400);transform:scale(1.05)}.exercises-header .header-top .header-actions .action-icon-btn:active{transform:scale(.95)}.exercises-header .header-top .header-actions .action-icon-btn.search-btn{background:var(--primary-500)}.exercises-header .header-top .header-actions .action-icon-btn.search-btn:hover{background:var(--primary-400)}.exercises-header .header-top .header-actions .action-icon-btn.filter-btn{background:var(--secondary-500)}.exercises-header .header-top .header-actions .action-icon-btn.filter-btn:hover{background:var(--secondary-400)}.exercises-header .muscle-filter-bar{padding:var(--space-3) var(--space-4);display:flex;align-items:center;gap:var(--space-3)}.exercises-header .muscle-filter-bar .filter-icon-fixed{display:flex;align-items:center;justify-content:center;width:56px;height:56px;background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);color:var(--text-secondary);flex-shrink:0}.exercises-header .muscle-filter-bar .filter-icon-fixed svg{width:24px;height:24px}.exercises-header .muscle-filter-bar .muscle-filter-scroll{display:flex;gap:var(--space-3);overflow-x:auto;padding:var(--space-2) 0;-webkit-overflow-scrolling:touch;scrollbar-width:none;-ms-overflow-style:none;align-items:center;flex:1}.exercises-header .muscle-filter-bar .muscle-filter-scroll::-webkit-scrollbar{display:none}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn{display:flex;align-items:center;justify-content:center;width:56px;height:56px;background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);flex-shrink:0}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn .muscle-icon{width:56px!important;height:56px!important;max-width:none!important;max-height:none!important;object-fit:fill;display:block}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn:hover{border-color:var(--accent-300);background:var(--bg-hover);transform:scale(1.02)}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn.active{background:var(--bg-surface);border-color:var(--accent-500);border-width:2px;transform:scale(1.02)}.recent-performed-section{margin-bottom:var(--space-6);padding:0 var(--space-4)}.recent-performed-section .section-header{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-3)}.recent-performed-section .section-header h2{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.recent-performed-section .section-header .view-all-btn{background:none;border:none;color:var(--accent-500);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;padding:var(--space-1) var(--space-2);border-radius:var(--radius-md);transition:all var(--transition-normal) var(--ease-in-out)}.recent-performed-section .section-header .view-all-btn:hover{background:var(--bg-hover)}.recent-performed-section .recent-exercises-scroll{display:flex;gap:var(--space-3);overflow-x:auto;padding-bottom:var(--space-2);-webkit-overflow-scrolling:touch;scrollbar-width:none;-ms-overflow-style:none}.recent-performed-section .recent-exercises-scroll::-webkit-scrollbar{display:none}.recent-performed-section .recent-exercises-scroll .recent-exercise-card{flex-shrink:0;width:120px;background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);padding:var(--space-3);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.recent-performed-section .recent-exercises-scroll .recent-exercise-card:hover{border-color:var(--accent-500);transform:translateY(-2px);box-shadow:var(--shadow-md)}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-image{display:flex;justify-content:center;align-items:center;width:48px;height:48px;background:var(--accent-100);border-radius:var(--radius-full);margin:0 auto var(--space-2) auto}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-image .muscle-icon{font-size:24px}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-info{text-align:center}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-info h4{font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-primary);margin:0 0 var(--space-1) 0;line-height:1.2}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-info p{font-size:var(--text-xs);color:var(--text-tertiary);margin:0}.results-info{margin-bottom:var(--space-4);padding:0 var(--space-4)}.results-info .results-count{font-size:var(--text-sm);color:var(--text-secondary);font-weight:var(--font-medium)}.results-info .section-title{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.scroll-sentinel{height:20px;width:100%;margin:10px 0;opacity:0;pointer-events:none}@media (prefers-reduced-motion: reduce){.scroll-sentinel{opacity:.1;background:#ff00001a}}.exercises-grid{display:grid;margin-bottom:var(--space-8);contain:layout style;will-change:contents;grid-template-columns:1fr 1fr;column-gap:clamp(16px,4vw,24px);row-gap:clamp(20px,5vw,28px);padding:0 var(--space-4);overflow-x:hidden;width:100%;max-width:100%;align-items:stretch}@media (max-width: 320px){.exercises-grid{grid-template-columns:1fr 1fr;column-gap:12px;row-gap:16px;padding:0 var(--space-3)}}@media (min-width: 321px) and (max-width: 375px){.exercises-grid{grid-template-columns:1fr 1fr;column-gap:14px;row-gap:18px;padding:0 var(--space-3)}}@media (min-width: 376px) and (max-width: 390px){.exercises-grid{grid-template-columns:1fr 1fr;column-gap:16px;row-gap:20px;padding:0 var(--space-4)}}@media (min-width: 391px) and (max-width: 480px){.exercises-grid{grid-template-columns:1fr 1fr;column-gap:18px;row-gap:22px;padding:0 var(--space-4)}}@media (min-width: 481px) and (max-width: 767px){.exercises-grid{grid-template-columns:repeat(3,1fr);column-gap:var(--space-4);row-gap:var(--space-5);padding:0 var(--space-4)}}@media (min-width: 768px) and (max-width: 1023px){.exercises-grid{grid-template-columns:repeat(auto-fill,minmax(280px,1fr));column-gap:var(--space-4);row-gap:var(--space-5);padding:0 var(--space-5)}}@media (min-width: 1024px){.exercises-grid{grid-template-columns:repeat(auto-fill,minmax(300px,1fr));column-gap:var(--space-5);row-gap:var(--space-6);padding:0 var(--space-6)}}@media (max-width: 768px) and (orientation: landscape){.exercises-grid{column-gap:clamp(14px,3vw,18px);row-gap:clamp(16px,4vw,20px);padding:0 var(--space-4)}}@media (max-width: 768px) and (orientation: landscape) and (min-width: 568px){.exercises-grid{grid-template-columns:repeat(3,1fr)}}.exercise-card{background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-xl);overflow:hidden;transition:all var(--transition-normal) var(--ease-in-out);display:flex;flex-direction:column;aspect-ratio:3/4;min-width:0;min-height:180px;-webkit-tap-highlight-color:transparent;touch-action:manipulation}@media (max-width: 320px){.exercise-card{aspect-ratio:4/5}}@media (min-width: 321px) and (max-width: 375px){.exercise-card{aspect-ratio:3.2/4}}.exercise-card:hover{border-color:var(--accent-500);transform:translateY(-4px);box-shadow:var(--shadow-xl)}.exercise-card:active{transform:translateY(-2px) scale(.98)}.exercise-card .exercise-image{position:relative;flex:1;background:var(--primary-600);min-height:120px}.exercise-card .exercise-image img{width:100%;height:100%;object-fit:cover}.exercise-card .exercise-image .favorite-btn{position:absolute;top:var(--space-2);right:var(--space-2);background:#00000080;border:none;border-radius:var(--radius-full);color:#fff;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);display:flex;align-items:center;justify-content:center;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);width:clamp(32px,8vw,40px);height:clamp(32px,8vw,40px)}.exercise-card .exercise-image .favorite-btn svg{width:clamp(16px,4vw,20px);height:clamp(16px,4vw,20px);stroke-width:2}.exercise-card .exercise-image .favorite-btn:hover{background:#000000b3;transform:scale(1.1)}.exercise-card .exercise-image .favorite-btn:active{transform:scale(.95)}.exercise-card .exercise-image .favorite-btn.favorited{color:var(--error-500);background:#ffffffe6}.exercise-card .exercise-info{flex-shrink:0;padding:clamp(8px,2.5vw,12px)}.exercise-card .exercise-info .exercise-name{color:var(--text-primary);margin:0 0 var(--space-2) 0;line-height:var(--leading-tight);display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden;font-size:clamp(.875rem,3.5vw,1rem);font-weight:var(--font-semibold)}@media (max-width: 320px){.exercise-card .exercise-info .exercise-name{font-size:.875rem;-webkit-line-clamp:1}}.exercise-card .exercise-info .exercise-meta{display:flex;justify-content:space-between;align-items:center;margin-bottom:var(--space-1);gap:var(--space-1)}.exercise-card .exercise-info .exercise-meta .difficulty{color:var(--accent-500);font-size:clamp(.75rem,3vw,.875rem)}.exercise-card .exercise-info .exercise-meta .difficulty .difficulty-plate{width:clamp(16px,4vw,20px);height:clamp(16px,4vw,20px);object-fit:contain;vertical-align:middle}.exercise-card .exercise-info .exercise-meta .category{background:var(--primary-100);color:var(--primary-700);font-weight:var(--font-medium);border-radius:var(--radius-md);font-size:clamp(.625rem,2.5vw,.75rem);padding:clamp(2px,1vw,4px) clamp(4px,2vw,8px)}@media (max-width: 320px){.exercise-card .exercise-info .exercise-meta .category{display:none}}.empty-state{display:flex;flex-direction:column;align-items:center;justify-content:center;text-align:center;padding:var(--space-16) var(--space-8)}.empty-state .empty-icon{width:4rem;height:4rem;margin-bottom:var(--space-4);color:var(--text-tertiary)}.empty-state .empty-icon svg{width:100%;height:100%;stroke-width:1.5}.empty-state h3{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0 0 var(--space-2) 0}.empty-state p{color:var(--text-secondary);margin:0}.exercise-detail-modal{background:var(--bg-surface);border:1px solid var(--primary-500);border-radius:var(--radius-xl);width:100%;max-width:700px;max-height:90vh;overflow:hidden;display:flex;flex-direction:column;box-shadow:var(--shadow-2xl)}.exercise-detail-modal .modal-header{display:flex;justify-content:space-between;align-items:flex-start;padding:var(--space-6);border-bottom:1px solid var(--primary-500)}.exercise-detail-modal .modal-header .title-group{flex:1}.exercise-detail-modal .modal-header .title-group h2{font-size:var(--text-2xl);font-weight:var(--font-bold);color:var(--text-primary);margin:0 0 var(--space-1) 0}.exercise-detail-modal .modal-header .title-group .english-name{font-size:var(--text-base);color:var(--text-tertiary);margin:0;font-style:italic}.exercise-detail-modal .modal-header .close-btn{padding:var(--space-2);background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.exercise-detail-modal .modal-header .close-btn svg{width:1.5rem;height:1.5rem;stroke-width:2}.exercise-detail-modal .modal-header .close-btn:hover{background:var(--primary-600);color:var(--text-primary)}.exercise-detail-modal .modal-content{flex:1;overflow-y:auto;padding:var(--space-6)}.exercise-detail-modal .modal-content .exercise-images{margin-bottom:var(--space-6);border-radius:var(--radius-lg);overflow:hidden}.exercise-detail-modal .modal-content .exercise-images img{width:100%;height:auto;display:block}.exercise-detail-modal .modal-content .exercise-details .detail-section{margin-bottom:var(--space-6)}.exercise-detail-modal .modal-content .exercise-details .detail-section .instructions-list{list-style:none;padding:0;margin:0}.exercise-detail-modal .modal-content .exercise-details .detail-section .instructions-list li{padding:var(--space-2) 0;border-bottom:1px solid var(--border-color);color:var(--text-primary)}.exercise-detail-modal .modal-content .exercise-details .detail-section .instructions-list li:last-child{border-bottom:none}.exercise-detail-modal .modal-content .exercise-details .detail-section .instructions-list li:before{content:counter(step-counter);counter-increment:step-counter;background:var(--accent-500);color:var(--text-on-accent);width:1.5rem;height:1.5rem;border-radius:var(--radius-full);display:inline-flex;align-items:center;justify-content:center;font-size:var(--text-xs);font-weight:var(--font-bold);margin-right:var(--space-3)}.exercise-detail-modal .modal-content .exercise-details .detail-section .tips-list{list-style:none;padding:0;margin:0}.exercise-detail-modal .modal-content .exercise-details .detail-section .tips-list li{padding:var(--space-2) 0;color:var(--text-primary);position:relative;padding-left:var(--space-6)}.exercise-detail-modal .modal-content .exercise-details .detail-section .tips-list li:before{content:"💡";position:absolute;left:0;top:var(--space-2)}.exercise-detail-modal .modal-content .exercise-details .detail-section .alternatives-list{display:flex;flex-wrap:wrap;gap:var(--space-2)}.exercise-detail-modal .modal-content .exercise-details .detail-section .alternatives-list .alternative-tag{background:var(--primary-100);color:var(--primary-700);padding:var(--space-1) var(--space-3);border-radius:var(--radius-full);font-size:var(--text-sm);font-weight:var(--font-medium)}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:#00000080;display:flex;align-items:center;justify-content:center;padding:var(--space-4);z-index:1000;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.search-modal{background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-xl);width:100%;max-width:500px;box-shadow:var(--shadow-2xl)}.search-modal .modal-header{display:flex;justify-content:space-between;align-items:center;padding:var(--space-4);border-bottom:1px solid var(--border-color)}.search-modal .modal-header h3{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.search-modal .modal-header .close-btn{width:32px;height:32px;background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);display:flex;align-items:center;justify-content:center}.search-modal .modal-header .close-btn svg{width:18px;height:18px;stroke-width:2}.search-modal .modal-header .close-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.search-modal .search-content{padding:var(--space-4)}.search-modal .search-content .search-bar{position:relative}.search-modal .search-content .search-bar svg{position:absolute;left:var(--space-3);top:50%;transform:translateY(-50%);width:20px;height:20px;color:var(--text-tertiary);stroke-width:2}.search-modal .search-content .search-bar input{width:100%;padding:var(--space-3) var(--space-3) var(--space-3) 3rem;background:var(--bg-primary);border:1px solid var(--border-color);border-radius:var(--radius-lg);color:var(--text-primary);font-size:var(--text-base);transition:all var(--transition-normal) var(--ease-in-out)}.search-modal .search-content .search-bar input::placeholder{color:var(--text-tertiary)}.search-modal .search-content .search-bar input:focus{outline:2px solid var(--accent-500);outline-offset:2px;border-color:var(--accent-500)}.filter-modal{background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-xl);width:100%;max-width:600px;max-height:80vh;overflow:hidden;display:flex;flex-direction:column;box-shadow:var(--shadow-2xl)}.filter-modal .modal-header{display:flex;justify-content:space-between;align-items:center;padding:var(--space-4);border-bottom:1px solid var(--border-color)}.filter-modal .modal-header h3{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.filter-modal .modal-header .close-btn{width:32px;height:32px;background:transparent;border:none;border-radius:var(--radius-md);color:var(--text-tertiary);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);display:flex;align-items:center;justify-content:center}.filter-modal .modal-header .close-btn svg{width:18px;height:18px;stroke-width:2}.filter-modal .modal-header .close-btn:hover{background:var(--bg-hover);color:var(--text-primary)}.filter-modal .filter-content{flex:1;overflow-y:auto;padding:var(--space-4);display:grid;gap:var(--space-4)}.filter-modal .filter-content .filter-group{display:flex;flex-direction:column;gap:var(--space-2)}.filter-modal .filter-content .filter-group label{font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-secondary)}.filter-modal .filter-content .filter-group select{padding:var(--space-3);background:var(--bg-primary);border:1px solid var(--border-color);border-radius:var(--radius-md);color:var(--text-primary);font-size:var(--text-sm);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.filter-modal .filter-content .filter-group select:hover{border-color:var(--accent-500)}.filter-modal .filter-content .filter-group select:focus{outline:2px solid var(--accent-500);outline-offset:2px;border-color:var(--accent-500)}.filter-modal .filter-content .filter-toggles .toggle-label{display:flex;align-items:center;gap:var(--space-2);cursor:pointer;padding:var(--space-2);border-radius:var(--radius-md);transition:background-color var(--transition-normal) var(--ease-in-out)}.filter-modal .filter-content .filter-toggles .toggle-label:hover{background:var(--bg-hover)}.filter-modal .filter-content .filter-toggles .toggle-label input[type=checkbox]{width:18px;height:18px;accent-color:var(--accent-500)}.filter-modal .filter-content .filter-toggles .toggle-label span{font-size:var(--text-sm);color:var(--text-primary);font-weight:var(--font-medium)}.filter-modal .filter-content .sort-section{display:flex;flex-direction:column;gap:var(--space-2)}.filter-modal .filter-content .sort-section label{font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-secondary)}.filter-modal .filter-content .sort-section select{padding:var(--space-3);background:var(--accent-500);border:1px solid var(--accent-500);border-radius:var(--radius-md);color:var(--text-on-accent);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.filter-modal .filter-content .sort-section select:hover{background:var(--accent-400);border-color:var(--accent-400)}.filter-modal .filter-content .sort-section select:focus{outline:2px solid var(--accent-500);outline-offset:2px}@media (max-width: 768px){.exercises-header .header-top{padding:var(--space-3) var(--space-3) var(--space-2) var(--space-3);padding-top:calc(env(safe-area-inset-top) + var(--space-3))}.exercises-header .header-top .title-section h1{font-size:var(--text-2xl)}.exercises-header .header-top .header-actions{gap:var(--space-1)}.exercises-header .header-top .header-actions .action-icon-btn{width:40px;height:40px}.exercises-header .header-top .header-actions .action-icon-btn svg{width:18px;height:18px}.exercises-header .muscle-filter-bar{padding:0 var(--space-3) var(--space-3) var(--space-3)}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn{min-width:55px;padding:var(--space-1) var(--space-2)}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn .muscle-icon{font-size:18px}.exercises-header .muscle-filter-bar .muscle-filter-scroll .muscle-filter-btn .muscle-name{font-size:10px}.recent-performed-section{padding:0 var(--space-3)}.recent-performed-section .recent-exercises-scroll .recent-exercise-card{width:100px;padding:var(--space-2)}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-image{width:40px;height:40px}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-image .muscle-icon{font-size:20px}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-info h4{font-size:11px}.recent-performed-section .recent-exercises-scroll .recent-exercise-card .recent-exercise-info p{font-size:10px}.results-info,.exercises-grid{padding:0 var(--space-3)}.modal-overlay{padding:var(--space-3)}.search-modal,.filter-modal{max-width:none;width:100%}}.search-bar input:focus,.filter-group select:focus,.sort-section select:focus,.action-btn:focus,.favorite-btn:focus,.close-btn:focus{outline:2px solid var(--accent-500);outline-offset:2px}.loading-state{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--space-8) var(--space-4);text-align:center}.loading-state .loading-spinner{width:48px;height:48px;margin-bottom:var(--space-4)}.loading-state .loading-spinner svg{width:100%;height:100%;color:var(--accent-500);animation:spin 1s linear infinite}.loading-state p{color:var(--text-secondary);font-size:var(--text-base);margin:0}.error-state{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--space-8) var(--space-4);text-align:center}.error-state .error-icon{width:48px;height:48px;margin-bottom:var(--space-4)}.error-state .error-icon svg{width:100%;height:100%;color:var(--error-500)}.error-state h3{color:var(--text-primary);font-size:var(--text-lg);font-weight:var(--font-semibold);margin:0 0 var(--space-2) 0}.error-state p{color:var(--text-secondary);font-size:var(--text-base);margin:0 0 var(--space-4) 0;max-width:300px}.error-state .retry-btn{background:var(--accent-500);color:var(--text-on-accent);border:none;padding:var(--space-3) var(--space-6);border-radius:var(--radius-md);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.error-state .retry-btn:hover{background:var(--accent-400);transform:translateY(-1px)}.error-state .retry-btn:active{transform:translateY(0)}.error-state .retry-btn:focus{outline:2px solid var(--accent-500);outline-offset:2px}@media (prefers-reduced-motion: reduce){.exercise-card,.action-btn,.favorite-btn{transition:none}.exercise-card:hover,.action-btn:hover,.favorite-btn:hover{transform:none}.loading-spinner svg{animation:none}.retry-btn:hover{transform:none}}.debug-panel{margin:var(--space-6) var(--space-4);padding:var(--space-4);background:var(--bg-surface);border:2px solid var(--error-500);border-radius:var(--radius-lg)}.debug-panel h3{margin:0 0 var(--space-3) 0;color:var(--error-500);font-size:var(--text-lg)}.debug-panel .debug-info{margin-bottom:var(--space-3)}.debug-panel .debug-info p{margin:var(--space-1) 0;font-family:monospace;font-size:var(--text-sm);color:var(--text-secondary)}.debug-panel .debug-buttons{display:flex;gap:var(--space-2)}.debug-panel .debug-buttons button{padding:var(--space-2) var(--space-3);background:var(--error-500);color:var(--text-on-error);border:none;border-radius:var(--radius-md);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.debug-panel .debug-buttons button:hover:not(:disabled){background:var(--error-400);transform:translateY(-1px)}.debug-panel .debug-buttons button:disabled{opacity:.5;cursor:not-allowed}.exercise-card{background-color:#fff;border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000001a;transition:all .3s ease;cursor:pointer;position:relative;display:flex;flex-direction:column;height:100%;aspect-ratio:.8}.exercise-card:hover{transform:translateY(-2px);box-shadow:0 4px 16px #00000026}.exercise-card:active{transform:translateY(0)}@media (max-width: 480px){.exercise-card{border-radius:10px;box-shadow:0 1px 4px #0000001a;aspect-ratio:.85}.exercise-card:hover{transform:translateY(-1px);box-shadow:0 2px 8px #0000001f}}@media (max-width: 360px){.exercise-card{border-radius:8px;box-shadow:0 1px 3px #00000014;aspect-ratio:.9}.exercise-card:hover{transform:none;box-shadow:0 1px 4px #0000001a}}.exercise-image{position:relative;width:100%;flex:1;overflow:hidden;background-color:#f5f5f5;min-height:100px}@media (max-width: 480px){.exercise-image{min-height:80px}}@media (max-width: 360px){.exercise-image{min-height:70px}}.image-container{position:relative;width:100%;height:100%}.image-placeholder{position:absolute;top:0;left:0;width:100%;height:100%;background:linear-gradient(90deg,#f0f0f0 25%,#e0e0e0,#f0f0f0 75%);background-size:200% 100%;animation:shimmer 1.5s infinite}@keyframes shimmer{0%{background-position:-200% 0}to{background-position:200% 0}}.exercise-img{width:100%;height:100%;object-fit:cover;opacity:0;transition:opacity .3s ease}.exercise-img.loaded{opacity:1}.favorite-btn{position:absolute;top:8px;right:8px;width:32px;height:32px;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;z-index:2;background:#0000004d;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px);border-radius:50%;transition:all .2s ease}.favorite-btn svg{width:18px;height:18px;color:#fff;stroke-width:2;transition:color .2s ease}.favorite-btn .like-icon{width:16px;height:16px;object-fit:contain}.favorite-btn:hover{background:#0006;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);transform:scale(1.05)}.favorite-btn:hover svg{color:#fff}.favorite-btn:active{transform:scale(.95)}.favorite-btn:focus{outline:none}.favorite-btn.favorited{background:none!important;backdrop-filter:none!important;-webkit-backdrop-filter:none!important;border-radius:0}.favorite-btn.favorited:hover{background:none!important;backdrop-filter:none!important;-webkit-backdrop-filter:none!important;transform:scale(1.05)}.favorite-btn.favorited:active{transform:scale(.95)}.exercise-info{padding:12px;display:flex;flex-direction:column;gap:6px;flex-shrink:0}@media (max-width: 480px){.exercise-info{padding:10px;gap:5px}}@media (max-width: 360px){.exercise-info{padding:8px;gap:4px}}.exercise-header{display:flex;justify-content:space-between;align-items:flex-start;gap:8px;margin-bottom:2px}@media (max-width: 480px){.exercise-header{gap:6px;margin-bottom:1px}}@media (max-width: 360px){.exercise-header{gap:4px;margin-bottom:0}}.exercise-name{font-size:14px;font-weight:600;color:#1a1a1a;margin:0;line-height:1.2;flex:1;min-width:0;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;overflow:hidden}@media (max-width: 480px){.exercise-name{font-size:13px;-webkit-line-clamp:2;line-height:1.1}}@media (max-width: 360px){.exercise-name{font-size:12px;-webkit-line-clamp:1;line-height:1.1}}.equipment-tags{flex-shrink:0;display:flex;align-items:flex-start}.equipment-tag{background-color:#f0f0f0;color:#666;font-size:11px;font-weight:500;padding:2px 6px;border-radius:4px;white-space:nowrap}@media (max-width: 480px){.equipment-tag{font-size:10px;padding:2px 4px}}@media (max-width: 360px){.equipment-tag{font-size:9px;padding:1px 3px}}.exercise-meta{display:flex;align-items:center;justify-content:space-between;gap:6px;margin-top:-4px}.exercise-meta .difficulty{display:flex;align-items:center;font-size:11px;color:#666;font-weight:500;margin-top:-2px}@media (max-width: 480px){.exercise-meta .difficulty{font-size:10px;margin-top:-1px}}@media (max-width: 360px){.exercise-meta .difficulty{font-size:9px;margin-top:-1px}}.exercise-meta .difficulty .difficulty-plate{width:14px;height:14px;object-fit:contain;vertical-align:middle;margin-right:3px}@media (max-width: 480px){.exercise-meta .difficulty .difficulty-plate{width:12px;height:12px;margin-right:2px}}@media (max-width: 360px){.exercise-meta .difficulty .difficulty-plate{width:10px;height:10px;margin-right:1px}}.exercise-meta .body-parts{display:flex;align-items:center;font-size:11px;color:#888;margin-top:-2px}@media (max-width: 480px){.exercise-meta .body-parts{margin-top:-1px}}@media (max-width: 360px){.exercise-meta .body-parts{margin-top:-1px}}.exercise-meta .body-parts .body-part-tag{background-color:#f0f8ff;color:#007aff;padding:1px 4px;border-radius:3px;font-weight:500;white-space:nowrap;font-size:11px}@media (max-width: 480px){.exercise-meta .body-parts .body-part-tag{font-size:10px;padding:1px 3px}}@media (max-width: 360px){.exercise-meta .body-parts .body-part-tag{font-size:9px;padding:0 2px}}.category,.exercise-details,.muscle-groups,.muscle-tag{display:none}@media (prefers-color-scheme: dark){.exercise-card{background-color:#1a1a1a;box-shadow:0 2px 8px #0000004d}.exercise-card:hover{box-shadow:0 4px 16px #0006}.exercise-image{background-color:#2a2a2a}.image-placeholder{background:linear-gradient(90deg,#2a2a2a 25%,#3a3a3a,#2a2a2a 75%)}.favorite-btn{background:#ffffff26;backdrop-filter:blur(8px);-webkit-backdrop-filter:blur(8px)}.favorite-btn svg{color:#fff}.favorite-btn:hover{background:#ffffff40;backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px)}.favorite-btn:hover svg{color:#fff}.favorite-btn.favorited,.favorite-btn.favorited:hover{background:none!important;backdrop-filter:none!important;-webkit-backdrop-filter:none!important}.exercise-name{color:#fff}.category{color:#a0a0a0;background-color:#2a2a2a}.equipment-tag:not(.more){color:#64d2ff;background-color:#64d2ff1a}.equipment-tag.more{color:#a0a0a0;background-color:#2a2a2a}.muscle-tag.primary{color:#64d2ff;background-color:#64d2ff1a}}.exercise-card{contain:layout style paint;will-change:transform}.exercise-img{will-change:opacity}@media (prefers-reduced-motion: reduce){.image-placeholder{animation:none;background:#f0f0f0}}.loading-spinner{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px}.loading-spinner.small{padding:10px}.loading-spinner.small .spinner{width:24px;height:24px}.loading-spinner.small .loading-text{font-size:12px;margin-top:8px}.loading-spinner.medium{padding:20px}.loading-spinner.medium .spinner{width:32px;height:32px}.loading-spinner.medium .loading-text{font-size:14px;margin-top:12px}.loading-spinner.large{padding:40px}.loading-spinner.large .spinner{width:48px;height:48px}.loading-spinner.large .loading-text{font-size:16px;margin-top:16px}.spinner{position:relative;display:flex;align-items:center;justify-content:center}.spinner-circle{width:8px;height:8px;border-radius:50%;background-color:#007aff;margin:0 2px;animation:spinner-bounce 1.4s ease-in-out infinite both}.spinner-circle:nth-child(1){animation-delay:-.32s}.spinner-circle:nth-child(2){animation-delay:-.16s}.spinner-circle:nth-child(3){animation-delay:0s}@keyframes spinner-bounce{0%,80%,to{transform:scale(0);opacity:.5}40%{transform:scale(1);opacity:1}}.loading-text{color:#666;font-weight:500;text-align:center;margin:0}.load-more-indicator{padding:20px;text-align:center;border-top:1px solid #f0f0f0;margin-top:20px}.load-more-indicator.loading{background-color:#fafafa}.load-more-indicator.error{background-color:#fff5f5;border-color:#fed7d7}.load-more-indicator.finished{background-color:#f0fff4;border-color:#c6f6d5}.error-content{display:flex;flex-direction:column;align-items:center;gap:12px}.error-icon{width:24px;height:24px;color:#e53e3e}.error-message{color:#e53e3e;font-weight:500;margin:0}.retry-btn{background-color:#007aff;color:#fff;border:none;border-radius:8px;padding:8px 16px;font-size:14px;font-weight:500;cursor:pointer;transition:background-color .2s ease}.retry-btn:hover{background-color:#0056b3}.retry-btn:active{transform:translateY(1px)}.finished-content{display:flex;flex-direction:column;align-items:center;gap:8px}.finished-icon{width:20px;height:20px;color:#38a169}.finished-message{color:#38a169;font-weight:500;margin:0;font-size:14px}.skeleton-card{background-color:#fff;border-radius:12px;padding:16px;box-shadow:0 2px 8px #0000001a;margin-bottom:16px;animation:skeleton-pulse 1.5s ease-in-out infinite}.skeleton-image{width:100%;height:120px;background-color:#e2e8f0;border-radius:8px;margin-bottom:12px}.skeleton-content{display:flex;flex-direction:column;gap:8px}.skeleton-title{height:20px;background-color:#e2e8f0;border-radius:4px;width:70%}.skeleton-meta{display:flex;gap:8px}.skeleton-tag{height:16px;background-color:#e2e8f0;border-radius:4px;width:60px}@keyframes skeleton-pulse{0%{opacity:1}50%{opacity:.7}to{opacity:1}}@media (prefers-color-scheme: dark){.loading-text{color:#a0a0a0}.load-more-indicator{border-color:#333}.load-more-indicator.loading{background-color:#1a1a1a}.load-more-indicator.error{background-color:#2d1b1b;border-color:#4a2c2c}.load-more-indicator.finished{background-color:#1b2d1b;border-color:#2c4a2c}.skeleton-card{background-color:#1a1a1a}.skeleton-image,.skeleton-title,.skeleton-tag{background-color:#333}}.exercise-detail-page{height:100vh;background:var(--bg-primary);display:flex;flex-direction:column;overflow:visible}@supports (-webkit-touch-callout: none){.exercise-detail-page{height:-webkit-fill-available}}.exercise-detail-header{display:flex;align-items:center;justify-content:space-between;padding:var(--space-4);padding-top:calc(env(safe-area-inset-top) + var(--space-4));background:var(--bg-primary);border-bottom:1px solid var(--border-color);position:fixed;top:0;left:0;right:0;z-index:var(--z-fixed);flex-shrink:0;min-height:calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8));backdrop-filter:blur(20px);-webkit-backdrop-filter:blur(20px);background:rgba(var(--bg-primary-rgb, 255, 255, 255),.95);box-shadow:var(--shadow-sm)}.exercise-detail-header .back-btn{width:var(--space-11);height:var(--space-11);border-radius:var(--radius-full);background:var(--bg-secondary);border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.exercise-detail-header .back-btn svg{width:20px;height:20px;color:var(--text-primary);stroke-width:2}.exercise-detail-header .back-btn:hover{background:var(--bg-hover);transform:scale(1.05)}.exercise-detail-header .back-btn:active{transform:scale(.95)}.exercise-detail-header .fixed-title{flex:1;text-align:center;margin:0 var(--space-4)}.exercise-detail-header .fixed-title h1{font-size:var(--text-xl);font-weight:var(--font-bold);color:var(--text-primary);margin:0;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;line-height:1.2}.exercise-detail-header .favorite-btn{width:var(--space-11);height:var(--space-11);border-radius:var(--radius-full);background:var(--bg-secondary);border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);align-self:center}.exercise-detail-header .favorite-btn svg{width:20px;height:20px;color:var(--text-secondary);stroke-width:2}.exercise-detail-header .favorite-btn:hover{background:var(--accent-500)}.exercise-detail-header .favorite-btn:hover svg{color:var(--text-on-accent)}.exercise-detail-header .favorite-btn:active{transform:scale(.95)}.exercise-detail-header .favorite-btn.favorited{background:var(--accent-500)}.exercise-detail-header .favorite-btn.favorited svg{color:var(--text-on-accent)}.exercise-detail-content{flex:1;display:flex;flex-direction:column;overflow:visible;padding-top:calc(env(safe-area-inset-top) + var(--space-11) + var(--space-8));-webkit-overflow-scrolling:touch;overscroll-behavior:none}.exercise-detail-content .video-section.fixed-video{flex-shrink:0;padding:var(--space-4);padding-bottom:0}.exercise-detail-content .exercise-info-section.fixed-info{flex-shrink:0;padding:var(--space-4);padding-top:0;padding-bottom:0}.exercise-detail-content .exercise-tabs-section.fixed-tabs{flex-shrink:0;padding:var(--space-4);padding-top:var(--space-2);padding-bottom:0;background:var(--bg-primary);border-bottom:1px solid var(--border-color);z-index:1}.exercise-detail-content .tab-content-scrollable{flex:1;overflow-y:auto;overflow-x:visible;padding:var(--space-4);padding-top:var(--space-4);padding-bottom:calc(env(safe-area-inset-bottom) + var(--space-8));-webkit-overflow-scrolling:touch;overscroll-behavior:none;scroll-behavior:smooth}.exercise-title-section{text-align:center}.exercise-title-section .exercise-title{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0 0 var(--space-2) 0}.exercise-title-section .exercise-meta{display:flex;justify-content:center;flex-wrap:wrap;gap:var(--space-3);margin-top:var(--space-2)}.exercise-title-section .exercise-meta .meta-item{font-size:var(--text-sm);color:var(--text-secondary);background:var(--bg-secondary);padding:var(--space-1) var(--space-2);border-radius:var(--radius-sm)}@media (max-width: 768px){.exercise-title-section .exercise-meta .meta-item{font-size:var(--text-xs)}}.exercise-info-section .exercise-info-card{background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);padding:var(--space-3)}.exercise-info-section .exercise-info-card .exercise-meta{display:flex;justify-content:space-between;align-items:center;gap:var(--space-3)}@media (max-width: 768px){.exercise-info-section .exercise-info-card .exercise-meta{flex-wrap:wrap;gap:var(--space-2)}}.exercise-info-section .exercise-info-card .exercise-meta .meta-item{font-size:var(--text-sm);color:var(--text-secondary)}@media (max-width: 768px){.exercise-info-section .exercise-info-card .exercise-meta .meta-item{font-size:var(--text-xs)}}.exercise-info-section .exercise-info-card .exercise-meta .difficulty{display:flex;align-items:center}.exercise-info-section .exercise-info-card .exercise-meta .difficulty .difficulty-plate{width:20px;height:20px;object-fit:contain;vertical-align:middle}.video-section.fixed-video{position:relative;z-index:1;background:var(--bg-primary)}.video-section .video-container{position:relative;width:100%;aspect-ratio:16/9;background:var(--bg-secondary);border-radius:var(--radius-lg);overflow:hidden;cursor:pointer}.video-section .video-container .exercise-video{width:100%;height:100%;object-fit:cover;border-radius:var(--radius-lg)}.video-section .video-container .video-controls-overlay{position:absolute;top:0;left:0;width:100%;height:100%;display:flex;align-items:center;justify-content:center;background:#0000004d;opacity:0;transition:opacity var(--transition-normal) var(--ease-in-out);pointer-events:none}.video-section .video-container .video-controls-overlay .play-pause-btn{width:60px;height:60px;border-radius:var(--radius-full);background:#ffffffe6;border:none;display:flex;align-items:center;justify-content:center;cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}.video-section .video-container .video-controls-overlay .play-pause-btn svg{width:24px;height:24px;color:var(--text-primary);stroke-width:2}.video-section .video-container .video-controls-overlay .play-pause-btn:hover{background:#fff;transform:scale(1.1)}.video-section .video-container .video-controls-overlay .play-pause-btn.paused svg{margin-left:2px}.video-section .video-container:hover .video-controls-overlay,.video-section .video-container:active .video-controls-overlay{opacity:1;pointer-events:auto}.exercise-tabs-section{margin-top:var(--space-6)}.exercise-tabs-section.fixed-tabs{margin-top:0}.exercise-tabs-section .exercise-tabs{--heroui-primary: var(--accent-500);--heroui-primary-foreground: var(--text-on-accent)}.exercise-tabs-section .exercise-tabs [role=tablist]{border-bottom:1px solid var(--border-color);margin-bottom:var(--space-4);background:transparent;padding:0}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab]{font-size:var(--text-base);font-weight:var(--font-medium);color:var(--text-secondary);padding:var(--space-3) var(--space-4);min-height:var(--space-11);transition:all var(--transition-normal) var(--ease-in-out);background:transparent;border:none;border-radius:0;position:relative}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab][aria-selected=true]{color:var(--accent-500);font-weight:var(--font-semibold);background:transparent}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab][aria-selected=true]:after{content:"";position:absolute;bottom:0;left:0;right:0;height:2px;background:var(--accent-500);border-radius:1px}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab]:hover{color:var(--text-primary);background:transparent}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab]:active{transform:scale(.98)}.exercise-tabs-section .exercise-tabs [role=tabpanel]{padding-top:var(--space-2);animation:fadeIn var(--transition-normal) var(--ease-in-out)}.exercise-tabs-section .exercise-tabs .tab-content-wrapper{padding:var(--space-4) 0;animation:fadeIn var(--transition-normal) var(--ease-in-out)}@keyframes fadeIn{0%{opacity:0;transform:translateY(8px)}to{opacity:1;transform:translateY(0)}}.muscle-info-section .muscle-info-card{background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);padding:var(--space-4);display:flex;gap:var(--space-4);align-items:flex-start;min-height:360px;overflow:visible;position:relative}@media (max-width: 768px){.muscle-info-section .muscle-info-card{gap:var(--space-3);min-height:320px;padding:var(--space-3)}}@media (max-width: 375px){.muscle-info-section .muscle-info-card{flex-direction:column;align-items:stretch;gap:var(--space-4);min-height:auto}}.muscle-info-section .muscle-list-sidebar{flex:0 0 20%;display:flex;flex-direction:column;gap:var(--space-2);min-width:100px;height:320px;overflow-y:auto;overflow-x:hidden;background:transparent;position:relative;z-index:1;-webkit-overflow-scrolling:touch;scrollbar-width:none;-ms-overflow-style:none}.muscle-info-section .muscle-list-sidebar::-webkit-scrollbar{display:none}.muscle-info-section .muscle-list-sidebar:after{content:"";position:sticky;bottom:0;left:0;right:0;height:20px;background:linear-gradient(transparent,var(--bg-surface));pointer-events:none;opacity:0;transition:opacity var(--transition-normal) var(--ease-in-out)}.muscle-info-section .muscle-list-sidebar.scrollable:after{opacity:1}@media (max-width: 768px){.muscle-info-section .muscle-list-sidebar{flex:0 0 20%;min-width:80px;height:240px}}@media (max-width: 375px){.muscle-info-section .muscle-list-sidebar{flex:none;width:100%;min-width:auto}}.muscle-info-section .muscle-list-sidebar .muscle-category{margin-bottom:var(--space-1);flex-shrink:0}.muscle-info-section .muscle-list-sidebar .muscle-category:last-child{margin-bottom:0}.muscle-info-section .muscle-list-sidebar .muscle-category h3{font-size:var(--text-sm);font-weight:var(--font-bold);color:var(--text-primary);margin:0 0 var(--space-2) 0;text-transform:uppercase;letter-spacing:.05em;position:relative;padding-bottom:var(--space-2);display:inline-block;width:auto;background-image:linear-gradient(transparent,transparent);background-repeat:no-repeat;background-size:100% 3px;background-position:0 calc(100% - 2px)}@media (max-width: 768px){.muscle-info-section .muscle-list-sidebar .muscle-category h3{font-size:var(--text-xs);margin:0 0 var(--space-1) 0;padding-bottom:var(--space-1)}}.muscle-info-section .muscle-list-sidebar .muscle-category:has(.muscle-tag.primary) h3{background-image:linear-gradient(var(--accent-500),var(--accent-500))}.muscle-info-section .muscle-list-sidebar .muscle-category:has(.muscle-tag.secondary) h3{background-image:linear-gradient(var(--accent-300),var(--accent-300))}.muscle-info-section .muscle-list-sidebar .muscle-category .muscle-tags{display:flex;flex-direction:column;gap:var(--space-2);align-items:flex-start;width:100%}.muscle-info-section .muscle-list-sidebar .muscle-category .muscle-tags .muscle-tag{display:inline-block;padding:var(--space-1) var(--space-2);border-radius:var(--radius-xl);font-size:var(--text-xs);font-weight:var(--font-medium);text-align:center;white-space:nowrap;width:fit-content;max-width:100%;transition:all var(--transition-normal) var(--ease-in-out);line-height:1.2;background:var(--bg-tertiary);color:var(--text-secondary);border:none}.muscle-info-section .muscle-list-sidebar .muscle-category .muscle-tags .muscle-tag:hover{transform:translateY(-1px);box-shadow:var(--shadow-sm);background:var(--bg-hover)}.muscle-info-section .muscle-list-sidebar .muscle-category .muscle-tags .muscle-tag:active{transform:scale(.98)}@media (max-width: 768px){.muscle-info-section .muscle-list-sidebar .muscle-category .muscle-tags .muscle-tag{padding:var(--space-1) var(--space-2);font-size:var(--text-xs)}}.muscle-info-section .muscle-illustration-container{flex:0 0 80%;display:flex;justify-content:flex-start;align-items:flex-start;min-height:320px;overflow:visible;position:relative;z-index:1100}.muscle-info-section .muscle-illustration-container>*{width:100%;height:auto;object-fit:contain}.muscle-info-section .muscle-illustration-container .static-muscle-illustration{width:100%;max-width:none;margin-top:0;position:relative;z-index:inherit}.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{width:100%;height:auto;display:block;max-width:none;position:relative;z-index:inherit}@supports (-webkit-touch-callout: none){.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{transform:translateZ(0);will-change:transform}}@media (max-width: 768px){.muscle-info-section .muscle-illustration-container{flex:0 0 80%;min-height:320px;align-items:flex-start}.muscle-info-section .muscle-illustration-container .static-muscle-illustration{max-width:none;margin-top:0}.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{max-width:none}@supports (-webkit-touch-callout: none){.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{transform:translateZ(0);will-change:transform;backface-visibility:hidden}}}@media (max-width: 375px){.muscle-info-section .muscle-illustration-container{flex:none;width:100%;min-height:280px}.muscle-info-section .muscle-illustration-container .static-muscle-illustration{max-width:none}.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{max-width:none}@supports (-webkit-touch-callout: none){.muscle-info-section .muscle-illustration-container .static-muscle-illustration svg{transform:translateZ(0);will-change:transform;backface-visibility:hidden}}}.muscle-info-section .muscle-illustration-container .no-muscle-data{text-align:center;color:var(--text-secondary)}.muscle-info-section .muscle-illustration-container .no-muscle-data p{margin:0;font-size:var(--text-sm)}.enhanced-muscle-illustration{display:flex;flex-direction:column;align-items:center;gap:var(--space-4)}.enhanced-muscle-illustration .muscle-svg{max-width:100%;height:auto;filter:drop-shadow(0 2px 4px rgba(0,0,0,.1))}.enhanced-muscle-illustration .muscle-legend{display:flex;gap:var(--space-4);justify-content:center;align-items:center}.enhanced-muscle-illustration .muscle-legend .legend-item{display:flex;align-items:center;gap:var(--space-2)}.enhanced-muscle-illustration .muscle-legend .legend-item .legend-color{width:16px;height:16px;border-radius:var(--radius-sm);border:1px solid var(--border-color)}.enhanced-muscle-illustration .muscle-legend .legend-item span{font-size:var(--text-sm);color:var(--text-secondary);font-weight:var(--font-medium)}.enhanced-muscle-illustration-loading{display:flex;justify-content:center;align-items:center;min-height:300px}.instructions-section .instruction-steps{display:flex;flex-direction:column;gap:var(--space-3)}.instructions-section .instruction-steps .instruction-step{display:flex;gap:var(--space-3);padding:var(--space-4);background:var(--bg-surface);border:1px solid var(--border-color);border-radius:var(--radius-lg);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out)}.instructions-section .instruction-steps .instruction-step:hover{border-color:var(--accent-300);background:var(--bg-hover)}.instructions-section .instruction-steps .instruction-step.active{border-color:var(--accent-500);background:var(--accent-50)}.instructions-section .instruction-steps .instruction-step .step-number{width:32px;height:32px;border-radius:var(--radius-full);background:var(--accent-500);color:var(--text-on-accent);display:flex;align-items:center;justify-content:center;font-size:var(--text-sm);font-weight:var(--font-semibold);flex-shrink:0}.instructions-section .instruction-steps .instruction-step .step-content{flex:1;color:var(--text-primary);font-size:var(--text-base);line-height:1.6}.tips-section .exercise-tips{display:flex;flex-direction:column;gap:var(--space-3)}.tips-section .exercise-tips .tip-item{display:flex;gap:var(--space-3);padding:var(--space-4);border-radius:var(--radius-lg);border-left:4px solid}.tips-section .exercise-tips .tip-item.warning{background:var(--warning-50);border-left-color:var(--warning-500)}.tips-section .exercise-tips .tip-item.warning .tip-icon svg{color:var(--warning-600)}.tips-section .exercise-tips .tip-item.info{background:var(--info-50);border-left-color:var(--info-500)}.tips-section .exercise-tips .tip-item.info .tip-icon svg{color:var(--info-600)}.tips-section .exercise-tips .tip-item .tip-icon{flex-shrink:0}.tips-section .exercise-tips .tip-item .tip-icon svg{width:20px;height:20px;stroke-width:2}.tips-section .exercise-tips .tip-item .tip-content{flex:1;color:var(--text-primary);font-size:var(--text-base);line-height:1.6}.header-placeholder{width:var(--space-11);height:var(--space-11)}.loading-state,.error-state{display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:calc(100vh - 200px);padding:var(--space-8);text-align:center}.loading-state .loading-spinner,.loading-state .error-icon,.error-state .loading-spinner,.error-state .error-icon{margin-bottom:var(--space-4)}.loading-state .loading-spinner svg,.loading-state .error-icon svg,.error-state .loading-spinner svg,.error-state .error-icon svg{width:48px;height:48px;color:var(--text-secondary)}.loading-state .loading-spinner svg.spinning,.loading-state .error-icon svg.spinning,.error-state .loading-spinner svg.spinning,.error-state .error-icon svg.spinning{animation:spin 1s linear infinite}.loading-state h3,.error-state h3{font-size:var(--text-xl);font-weight:var(--font-semibold);color:var(--text-primary);margin:0 0 var(--space-2) 0}.loading-state p,.error-state p{color:var(--text-secondary);font-size:var(--text-base);margin:0 0 var(--space-4) 0;max-width:300px;line-height:1.5}.loading-state .error-actions,.error-state .error-actions{display:flex;gap:var(--space-3)}.loading-state .error-actions .retry-btn,.loading-state .error-actions .back-btn-secondary,.error-state .error-actions .retry-btn,.error-state .error-actions .back-btn-secondary{padding:var(--space-3) var(--space-6);border-radius:var(--radius-lg);border:none;font-size:var(--text-base);font-weight:var(--font-medium);cursor:pointer;transition:all var(--transition-normal) var(--ease-in-out);min-height:var(--space-11)}.loading-state .error-actions .retry-btn,.error-state .error-actions .retry-btn{background:var(--accent-500);color:var(--text-on-accent)}.loading-state .error-actions .retry-btn:hover,.error-state .error-actions .retry-btn:hover{background:var(--accent-400)}.loading-state .error-actions .retry-btn:active,.error-state .error-actions .retry-btn:active{transform:scale(.98)}.loading-state .error-actions .back-btn-secondary,.error-state .error-actions .back-btn-secondary{background:var(--bg-secondary);color:var(--text-primary)}.loading-state .error-actions .back-btn-secondary:hover,.error-state .error-actions .back-btn-secondary:hover{background:var(--bg-hover)}.loading-state .error-actions .back-btn-secondary:active,.error-state .error-actions .back-btn-secondary:active{transform:scale(.98)}@media (max-width: 768px){@supports (-webkit-touch-callout: none){.exercise-detail-page{height:-webkit-fill-available}}.exercise-detail-header{padding:var(--space-3);padding-top:calc(env(safe-area-inset-top) + var(--space-3))}.exercise-detail-header .back-btn,.exercise-detail-header .favorite-btn{width:40px;height:40px}.exercise-detail-header .back-btn svg,.exercise-detail-header .favorite-btn svg{width:18px;height:18px}.exercise-detail-header .fixed-title h1{font-size:var(--text-base)}.exercise-detail-content{padding-top:calc(env(safe-area-inset-top) + 40px + var(--space-6))}.exercise-detail-content .video-section.fixed-video{padding:var(--space-3);padding-bottom:0}.exercise-detail-content .exercise-info-section.fixed-info{padding:var(--space-3);padding-top:0;padding-bottom:0}.exercise-detail-content .exercise-tabs-section.fixed-tabs{padding:var(--space-3);padding-top:var(--space-2);padding-bottom:0}.exercise-detail-content .tab-content-scrollable{padding:var(--space-3);padding-top:var(--space-4)}.exercise-tabs-section .exercise-tabs [role=tablist] [role=tab]{font-size:var(--text-sm);padding:var(--space-2) var(--space-3);min-height:var(--space-11)}}@media (min-width: 768px){.exercise-detail-content{max-width:800px;margin:0 auto;padding-left:var(--space-6);padding-right:var(--space-6)}}@media (min-width: 1024px){.exercise-detail-content{max-width:1000px;padding-left:var(--space-8);padding-right:var(--space-8)}}.theme-dark .exercise-detail-header,.theme-dark .exercise-tabs-section.fixed-tabs{background:rgba(var(--bg-primary-rgb, 15, 23, 42),.95)}.theme-dark .muscle-info-card{background:var(--bg-surface);border-color:var(--border-color)}.theme-dark .muscle-tag{background:var(--bg-tertiary)!important;color:var(--text-secondary)!important}.theme-dark .exercise-tabs [role=tablist]{border-bottom-color:var(--border-color)}.theme-dark .exercise-tabs [role=tablist] [role=tab]{color:var(--text-secondary)}.theme-dark .exercise-tabs [role=tablist] [role=tab][aria-selected=true]{color:var(--accent-400)}.theme-dark .exercise-tabs [role=tablist] [role=tab][aria-selected=true]:after{background:var(--accent-400)}.theme-dark .exercise-tabs [role=tablist] [role=tab]:hover{color:var(--text-primary)}@supports (-webkit-touch-callout: none){.exercise-detail-content{-webkit-overflow-scrolling:touch;overscroll-behavior:none;will-change:scroll-position}}.settings-page{padding:0}.settings-container{max-width:1200px;margin:0 auto;display:grid;grid-template-columns:280px 1fr;gap:var(--space-8, 2rem)}@media (max-width: 1024px){.settings-container{grid-template-columns:240px 1fr;gap:var(--space-6, 1.5rem)}}@media (max-width: 768px){.settings-container{grid-template-columns:1fr;gap:var(--space-4, 1rem)}}.settings-navigation{background:var(--bg-surface, #1e293b);border-radius:var(--radius-lg, .5rem);border:1px solid var(--border-primary, #334155);height:fit-content;position:sticky;top:var(--space-6, 1.5rem)}@media (max-width: 768px){.settings-navigation{position:static;border-radius:var(--radius-md, .375rem)}}.navigation-header{padding:var(--space-6, 1.5rem);border-bottom:1px solid var(--border-primary, #334155)}.navigation-header h1{font-size:var(--text-2xl, 1.5rem);font-weight:var(--font-bold, 700);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-2, .5rem) 0;background:linear-gradient(135deg,var(--accent-500, #3b82f6),var(--success-500, #22c55e));-webkit-background-clip:text;-webkit-text-fill-color:transparent;background-clip:text}.navigation-header p{font-size:var(--text-sm, .875rem);color:var(--text-secondary, #cbd5e1);margin:0}.navigation-menu{padding:var(--space-4, 1rem)}@media (max-width: 768px){.navigation-menu{display:flex;overflow-x:auto;gap:var(--space-2, .5rem);padding:var(--space-4, 1rem) var(--space-6, 1.5rem)}.navigation-menu::-webkit-scrollbar{display:none}}.nav-item{display:flex;align-items:center;gap:var(--space-3, .75rem);padding:var(--space-3, .75rem) var(--space-4, 1rem);border-radius:var(--radius-md, .375rem);border:none;background:transparent;color:var(--text-secondary, #cbd5e1);cursor:pointer;transition:all var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1));width:100%;text-align:left;margin-bottom:var(--space-1, .25rem)}.nav-item:hover{background:var(--primary-600, #1e293b);color:var(--text-primary, #f8fafc);transform:translate(4px)}.nav-item.active{background:linear-gradient(135deg,var(--accent-500, #3b82f6),var(--success-500, #22c55e));color:var(--text-on-accent, #ffffff);transform:translate(4px);box-shadow:var(--shadow-sm, 0 1px 2px 0 rgba(0, 0, 0, .05))}.nav-item.active .nav-icon{transform:scale(1.1)}@media (max-width: 768px){.nav-item{flex-direction:column;min-width:80px;text-align:center;gap:var(--space-1, .25rem)}.nav-item:hover,.nav-item.active{transform:translateY(-2px)}}.nav-icon{font-size:var(--text-lg, 1.125rem);transition:transform var(--transition-normal, .2s) var(--ease-in-out, cubic-bezier(.4, 0, .2, 1))}.nav-label{font-size:var(--text-sm, .875rem);font-weight:var(--font-medium, 500)}@media (max-width: 768px){.nav-label{font-size:var(--text-xs, .75rem)}}.settings-main{display:flex;flex-direction:column;gap:var(--space-6, 1.5rem)}.settings-content{background:var(--bg-surface, #1e293b);border-radius:var(--radius-lg, .5rem);border:1px solid var(--border-primary, #334155);overflow:hidden}.section{padding:var(--space-8, 2rem)}@media (max-width: 768px){.section{padding:var(--space-6, 1.5rem)}}.section-header{margin-bottom:var(--space-8, 2rem)}.section-header h2{font-size:var(--text-2xl, 1.5rem);font-weight:var(--font-bold, 700);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-2, .5rem) 0}.section-header p{font-size:var(--text-base, 1rem);color:var(--text-secondary, #cbd5e1);margin:0}.setting-group{margin-bottom:var(--space-8, 2rem)}.setting-group:last-child{margin-bottom:0}.setting-group h3{font-size:var(--text-lg, 1.125rem);font-weight:var(--font-semibold, 600);color:var(--text-primary, #f8fafc);margin:0 0 var(--space-6, 1.5rem) 0;padding-bottom:var(--space-2, .5rem);border-bottom:2px solid var(--border-primary, #334155)}.setting-row{display:grid;grid-template-columns:1fr 1fr;gap:var(--space-6)}@media (max-width: 768px){.setting-row{grid-template-columns:1fr;gap:var(--space-4)}}.setting-item{display:flex;justify-content:space-between;align-items:center;padding:var(--space-4) 0;border-bottom:1px solid var(--border-light);gap:var(--space-4)}.setting-item:last-child{border-bottom:none}.setting-item.half{flex:1}.setting-item.danger .setting-info label{color:var(--error-color)}@media (max-width: 768px){.setting-item{flex-direction:column;align-items:flex-start;gap:var(--space-3)}}.setting-info{flex:1}.setting-info label{display:block;font-size:var(--font-size-base);font-weight:500;color:var(--text-primary);margin-bottom:var(--space-1)}.setting-info .setting-desc{font-size:var(--font-size-sm);color:var(--text-secondary);line-height:1.4}.setting-input,.setting-select{padding:var(--space-3) var(--space-4);border:1px solid var(--border-color);border-radius:var(--border-radius-md);background:var(--surface-secondary);color:var(--text-primary);font-size:var(--font-size-sm);transition:all .2s ease;min-width:180px}.setting-input:focus,.setting-select:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 3px rgba(var(--primary-color-rgb),.1)}.setting-input:disabled,.setting-select:disabled{background:var(--surface-disabled);color:var(--text-disabled);cursor:not-allowed}@media (max-width: 768px){.setting-input,.setting-select{width:100%;min-width:auto}}.setting-button{padding:var(--space-2) var(--space-4);border:1px solid var(--border-color);border-radius:var(--border-radius-md);background:var(--surface-secondary);color:var(--text-primary);font-size:var(--font-size-sm);cursor:pointer;transition:all .2s ease;display:flex;align-items:center;gap:var(--space-2)}.setting-button:hover{background:var(--surface-color);transform:translateY(-1px);box-shadow:var(--shadow-sm)}.setting-button.primary{background:var(--primary-color);color:#fff;border-color:var(--primary-color)}.setting-button.primary:hover{background:var(--primary-color-hover);border-color:var(--primary-color-hover)}.setting-button.secondary{background:var(--surface-secondary);color:var(--text-primary)}.setting-button.danger{background:var(--error-color);color:#fff;border-color:var(--error-color)}.setting-button.danger:hover{background:var(--error-color-hover);border-color:var(--error-color-hover)}.setting-button .arrow{font-size:var(--font-size-sm);transition:transform .2s ease}.setting-button:hover .arrow{transform:translate(4px)}.setting-toggle{position:relative;width:52px;height:28px;cursor:pointer}.setting-toggle input{opacity:0;width:0;height:0}.setting-toggle .toggle-slider{position:absolute;cursor:pointer;top:0;left:0;right:0;bottom:0;background:var(--border-color);transition:all .3s ease;border-radius:24px}.setting-toggle .toggle-slider:before{position:absolute;content:"";height:20px;width:20px;left:4px;bottom:4px;background:#fff;transition:all .3s ease;border-radius:50%;box-shadow:var(--shadow-sm)}.setting-toggle input:checked+.toggle-slider{background:var(--primary-color)}.setting-toggle input:checked+.toggle-slider:before{transform:translate(24px)}.setting-toggle input:disabled+.toggle-slider{background:var(--surface-disabled);cursor:not-allowed}.setting-toggle input:disabled+.toggle-slider:before{background:var(--text-disabled)}.theme-selector{display:flex;gap:var(--space-2)}@media (max-width: 768px){.theme-selector{flex-direction:column}}.theme-option{display:flex;flex-direction:column;align-items:center;gap:var(--space-2);padding:var(--space-3);border:1px solid var(--border-color);border-radius:var(--border-radius-md);background:var(--surface-secondary);cursor:pointer;transition:all .2s ease;flex:1}.theme-option:hover{background:var(--surface-color);transform:translateY(-2px);box-shadow:var(--shadow-sm)}.theme-option.active{background:var(--primary-color);color:#fff;border-color:var(--primary-color);transform:translateY(-2px);box-shadow:var(--shadow-md)}.theme-option .theme-icon{font-size:var(--font-size-xl)}.theme-option .theme-label{font-size:var(--font-size-sm);font-weight:500}.app-info{display:flex;gap:var(--space-6);margin-bottom:var(--space-6);padding:var(--space-6);background:var(--surface-secondary);border-radius:var(--border-radius-md)}@media (max-width: 768px){.app-info{flex-direction:column;text-align:center}}.app-logo .logo-icon{width:80px;height:80px;background:linear-gradient(135deg,var(--primary-color),var(--secondary-color));border-radius:var(--border-radius-lg);display:flex;align-items:center;justify-content:center;font-size:2rem;box-shadow:var(--shadow-md)}.app-details{flex:1}.app-details h3{font-size:var(--font-size-xl);font-weight:700;color:var(--text-primary);margin:0 0 var(--space-2) 0}.app-details .version{font-size:var(--font-size-sm);color:var(--text-secondary);margin:0 0 var(--space-3) 0;font-weight:500}.app-details .description{font-size:var(--font-size-base);color:var(--text-secondary);line-height:1.6;margin:0}.storage-info{background:var(--surface-secondary);border-radius:var(--border-radius-md);padding:var(--space-4)}.storage-item{display:flex;justify-content:space-between;align-items:center;padding:var(--space-2) 0;border-bottom:1px solid var(--border-light)}.storage-item:last-child{border-bottom:none}.storage-item.total{font-weight:600;color:var(--text-primary);border-top:2px solid var(--border-color);padding-top:var(--space-3);margin-top:var(--space-2)}.storage-label{font-size:var(--font-size-sm);color:var(--text-secondary)}.storage-value{font-size:var(--font-size-sm);color:var(--text-primary);font-weight:500}.team-info{text-align:center;padding:var(--space-4);background:var(--surface-secondary);border-radius:var(--border-radius-md)}.team-info p{font-size:var(--font-size-sm);color:var(--text-secondary);margin:var(--space-1) 0}.settings-actions{position:sticky;bottom:0;background:var(--surface-color);border:1px solid var(--border-color);border-radius:var(--border-radius-lg);padding:var(--space-4);box-shadow:var(--shadow-lg);animation:slideUp .3s ease}@keyframes slideUp{0%{transform:translateY(100%);opacity:0}to{transform:translateY(0);opacity:1}}.actions-content{display:flex;justify-content:space-between;align-items:center}@media (max-width: 768px){.actions-content{flex-direction:column;gap:var(--space-3)}}.unsaved-indicator{display:flex;align-items:center;gap:var(--space-2);font-size:var(--font-size-sm);color:var(--warning-color)}.unsaved-indicator .indicator-dot{width:8px;height:8px;background:var(--warning-color);border-radius:50%;animation:pulse 2s infinite}@keyframes pulse{0%,to{opacity:1}50%{opacity:.5}}.action-buttons{display:flex;gap:var(--space-3)}.action-btn{padding:var(--space-3) var(--space-6);border:1px solid var(--border-color);border-radius:var(--border-radius-md);font-size:var(--font-size-sm);font-weight:500;cursor:pointer;transition:all .2s ease}.action-btn:disabled{opacity:.6;cursor:not-allowed}.action-btn.primary{background:var(--primary-color);color:#fff;border-color:var(--primary-color)}.action-btn.primary:hover:not(:disabled){background:var(--primary-color-hover);border-color:var(--primary-color-hover);transform:translateY(-1px);box-shadow:var(--shadow-md)}.action-btn.secondary{background:var(--surface-secondary);color:var(--text-primary)}.action-btn.secondary:hover:not(:disabled){background:var(--surface-color);transform:translateY(-1px);box-shadow:var(--shadow-sm)}.modal-overlay{position:fixed;top:0;left:0;right:0;bottom:0;background:#00000080;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);display:flex;justify-content:center;align-items:center;z-index:1000;animation:fadeIn .2s ease}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}.confirm-modal{background:var(--surface-color);border-radius:var(--border-radius-lg);padding:var(--space-6);max-width:440px;width:90%;border:1px solid var(--border-color);box-shadow:var(--shadow-xl);animation:slideIn .3s ease}@keyframes slideIn{0%{transform:translateY(-20px);opacity:0}to{transform:translateY(0);opacity:1}}.modal-header{margin-bottom:var(--space-4)}.modal-header h3{font-size:var(--font-size-lg);font-weight:600;color:var(--text-primary);margin:0}.modal-content{margin-bottom:var(--space-6)}.modal-content p{font-size:var(--font-size-base);color:var(--text-secondary);line-height:1.6;margin:0 0 var(--space-3) 0}.modal-content p:last-child{margin-bottom:0}.modal-actions{display:flex;gap:var(--space-3);justify-content:flex-end}.modal-button{padding:var(--space-3) var(--space-6);border:1px solid var(--border-color);border-radius:var(--border-radius-md);font-size:var(--font-size-sm);font-weight:500;cursor:pointer;transition:all .2s ease}.modal-button.primary{background:var(--primary-color);color:#fff;border-color:var(--primary-color)}.modal-button.primary:hover{background:var(--primary-color-hover);border-color:var(--primary-color-hover)}.modal-button.secondary{background:var(--surface-secondary);color:var(--text-primary)}.modal-button.secondary:hover{background:var(--surface-color)}.modal-button.danger{background:var(--error-color);color:#fff;border-color:var(--error-color)}.modal-button.danger:hover{background:var(--error-color-hover);border-color:var(--error-color-hover)}@media (max-width: 568px){.settings-page{padding:var(--space-2)}.settings-container{gap:var(--space-3)}.section{padding:var(--space-4)}.section-header{margin-bottom:var(--space-6)}.section-header h2{font-size:var(--font-size-xl)}.setting-group{margin-bottom:var(--space-6)}.setting-item{padding:var(--space-3) 0}.app-info{padding:var(--space-4)}.app-logo .logo-icon{width:60px;height:60px;font-size:1.5rem}}[data-theme=dark] .theme-option.active{box-shadow:0 4px 12px rgba(var(--primary-color-rgb),.3)}[data-theme=dark] .setting-toggle .toggle-slider:before{box-shadow:0 2px 4px #0003}[data-theme=dark] .confirm-modal{box-shadow:0 20px 40px #0006}.ui-test-page{padding:24px;max-width:1200px;margin:0 auto}.ui-test-page .page-header{margin-bottom:32px;border-bottom:1px solid #e0e0e0;padding-bottom:16px}.ui-test-page .page-header h1{font-size:28px;margin-bottom:8px}.ui-test-page .page-header .description{color:#666;font-size:16px}.ui-test-page .test-section{margin-bottom:48px}.ui-test-page .test-section h2{font-size:24px;margin-bottom:16px;padding-bottom:8px;border-bottom:1px solid #eaeaea}.ui-test-page .component-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:24px}.ui-test-page .component-card{border:1px solid #e0e0e0;border-radius:8px;overflow:hidden;background-color:#fff;box-shadow:0 2px 4px #0000000d}.ui-test-page .component-card h3{font-size:18px;padding:12px 16px;margin:0;background-color:#f5f5f5;border-bottom:1px solid #e0e0e0}.ui-test-page .component-card .component-preview{padding:20px;min-height:120px;display:flex;flex-direction:column;gap:12px}.ui-test-page .component-card .component-preview.icons-preview{display:grid;grid-template-columns:repeat(auto-fill,minmax(80px,1fr));gap:16px}.ui-test-page .component-card .component-preview.icons-preview .icon-item{display:flex;flex-direction:column;align-items:center;text-align:center}.ui-test-page .component-card .component-preview.icons-preview .icon-item i{font-size:24px;margin-bottom:8px;padding:8px;border-radius:8px;background:#f8f8f8;width:40px;height:40px;display:flex;align-items:center;justify-content:center;color:#333;transition:all .2s ease}.ui-test-page .component-card .component-preview.icons-preview .icon-item i:hover{transform:scale(1.1);background:#eaeaea}.ui-test-page .component-card .component-preview.icons-preview .icon-item .icon-label{font-size:12px;color:#666;margin-top:4px}.ui-test-page .component-card .component-preview.app-icons-preview .app-icon-item{display:flex;flex-direction:column;align-items:center;margin-bottom:16px}.ui-test-page .component-card .component-preview.app-icons-preview .app-icon-item .icon{background-color:#f5f5f5;padding:8px;border-radius:8px;margin-bottom:8px;transition:all .2s ease}.ui-test-page .component-card .component-preview.app-icons-preview .app-icon-item .icon:hover{transform:scale(1.1);background-color:#eaeaea}.ui-test-page .component-card .component-preview.app-icons-preview .app-icon-item .icon-label{font-size:12px;color:#666}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes{margin-top:24px;border-top:1px solid #eaeaea;padding-top:16px}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes h4{margin-bottom:16px;font-size:16px;color:#333}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes .size-examples{display:flex;justify-content:space-around;align-items:flex-end;flex-wrap:wrap;gap:16px}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes .size-examples .size-example{display:flex;flex-direction:column;align-items:center}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes .size-examples .size-example .icon{margin-bottom:8px;background-color:#f5f5f5;border-radius:8px;padding:8px}.ui-test-page .component-card .component-preview.app-icons-preview .icon-sizes .size-examples .size-example span{font-size:12px;color:#666}.ui-test-page .component-card .component-preview .placeholder{color:#999;font-style:italic;text-align:center;border:1px dashed #ccc;padding:20px;border-radius:4px}.ui-test-page .performance-tests{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:24px}.ui-test-page .performance-tests .test-card{border:1px solid #e0e0e0;border-radius:8px;overflow:hidden}.ui-test-page .performance-tests .test-card h3{font-size:18px;padding:12px 16px;margin:0;background-color:#f5f5f5;border-bottom:1px solid #e0e0e0}.ui-test-page .performance-tests .test-card .test-content{padding:16px;min-height:100px}.ui-test-page .performance-tests .test-card .test-content p{margin:0;line-height:1.6}.ui-test-page .performance-tests .test-card .test-content p strong{color:var(--primary-color)}.ui-test-page.theme-dark{background-color:#1a1a1a;color:#e0e0e0}.ui-test-page.theme-dark .page-header{border-bottom-color:#333}.ui-test-page.theme-dark .page-header .description{color:#aaa}.ui-test-page.theme-dark .test-section h2{border-bottom-color:#333}.ui-test-page.theme-dark .component-card{border-color:#333;background-color:#252525;box-shadow:0 2px 4px #0003}.ui-test-page.theme-dark .component-card h3{background-color:#333;border-bottom-color:#444}.ui-test-page.theme-dark .component-card .component-preview.icons-preview .icon-item i{background:#333;color:#e0e0e0}.ui-test-page.theme-dark .component-card .component-preview.icons-preview .icon-item i:hover{background:#444}.ui-test-page.theme-dark .component-card .component-preview.icons-preview .icon-item .icon-label{color:#aaa}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .app-icon-item .icon{background-color:#333;color:#e0e0e0}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .app-icon-item .icon:hover{background-color:#444}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .app-icon-item .icon-label{color:#aaa}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .icon-sizes{border-top-color:#444}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .icon-sizes h4{color:#e0e0e0}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .icon-sizes .size-example .icon{background-color:#333;color:#e0e0e0}.ui-test-page.theme-dark .component-card .component-preview.app-icons-preview .icon-sizes .size-example span{color:#aaa}.ui-test-page.theme-dark .component-card .component-preview .placeholder{color:#777;border-color:#444}.ui-test-page.theme-dark .performance-tests .test-card{border-color:#333}.ui-test-page.theme-dark .performance-tests .test-card h3{background-color:#333;border-bottom-color:#444}.icon-showcase{max-width:1200px;margin:0 auto;padding:24px;font-family:Arial,sans-serif}.icon-showcase .showcase-header{margin-bottom:40px}.icon-showcase .showcase-header h1{font-size:32px;margin-bottom:12px;color:var(--primary-color)}.icon-showcase .showcase-header .description{font-size:16px;color:var(--text-secondary, #666);max-width:800px;margin-bottom:24px}.icon-showcase .showcase-header .guide-link-container{margin-bottom:20px}.icon-showcase .showcase-header .guide-link-container .guide-link{display:inline-flex;align-items:center;gap:8px;padding:8px 16px;background:var(--surface-color, #fff);border:1px solid var(--border-color, #e0e0e0);border-radius:8px;text-decoration:none;color:var(--primary-color, #5c6bc0);font-weight:500;transition:all .2s}.icon-showcase .showcase-header .guide-link-container .guide-link:hover{background:var(--surface-secondary, #f9fafb);transform:translateY(-1px);box-shadow:0 2px 5px #0000000d}.icon-showcase .showcase-header .guide-link-container .guide-link .icon{color:var(--primary-color, #5c6bc0)}.icon-showcase .showcase-header .guide-link-container .guide-link span{flex:1}.icon-showcase .showcase-header .search-container{position:relative;max-width:500px}.icon-showcase .showcase-header .search-container .search-input{width:100%;padding:12px 40px 12px 16px;border-radius:8px;border:1px solid var(--border-color, #e0e0e0);font-size:16px}.icon-showcase .showcase-header .search-container .search-input:focus{outline:none;border-color:var(--primary-color);box-shadow:0 0 0 2px #5c6bc033}.icon-showcase .showcase-header .search-container .search-icon{position:absolute;right:12px;top:50%;transform:translateY(-50%);color:var(--text-secondary, #666)}.icon-showcase .icon-section{margin-bottom:48px}.icon-showcase .icon-section h2{font-size:24px;margin-bottom:12px;padding-bottom:8px;border-bottom:1px solid var(--border-color, #e0e0e0)}.icon-showcase .icon-section .section-desc{margin-bottom:20px;color:var(--text-secondary, #666)}.icon-showcase .icon-grid{display:grid;grid-template-columns:repeat(auto-fill,minmax(120px,1fr));gap:16px}.icon-showcase .icon-grid.all-icons{grid-template-columns:repeat(auto-fill,minmax(100px,1fr))}.icon-showcase .icon-grid .icon-item{display:flex;flex-direction:column;align-items:center;padding:16px 8px;border-radius:8px;background:var(--surface-color, #fff);border:1px solid var(--border-color, #e0e0e0);transition:all .2s ease}.icon-showcase .icon-grid .icon-item:hover{transform:translateY(-2px);box-shadow:0 4px 8px #0000001a;border-color:var(--primary-color)}.icon-showcase .icon-grid .icon-item .icon,.icon-showcase .icon-grid .icon-item i{font-size:24px;width:40px;height:40px;display:flex;align-items:center;justify-content:center;margin-bottom:12px;padding:8px;background:#00000008;border-radius:8px}.icon-showcase .icon-grid .icon-item .icon-name{font-size:12px;color:var(--primary-color);margin-bottom:4px;font-weight:500}.icon-showcase .icon-grid .icon-item .icon-label{font-size:12px;color:var(--text-secondary, #666)}.icon-showcase .size-examples{display:flex;justify-content:space-around;flex-wrap:wrap;gap:24px;background:var(--surface-color, #fff);border:1px solid var(--border-color, #e0e0e0);border-radius:8px;padding:24px}.icon-showcase .size-examples .size-example{display:flex;flex-direction:column;align-items:center}.icon-showcase .size-examples .size-example .icon{margin-bottom:12px;background:#00000008;border-radius:8px;padding:12px}.icon-showcase .size-examples .size-example .size-label{font-weight:500;margin-bottom:4px;color:var(--primary-color)}.icon-showcase .size-examples .size-example code{font-size:12px;background:#f5f5f5;padding:4px 8px;border-radius:4px}.icon-showcase .usage-examples{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:24px}.icon-showcase .usage-examples .usage-example h3{font-size:18px;margin-bottom:16px}.icon-showcase .usage-examples .usage-example .code-block{background:#1e1e1e;border-radius:8px;overflow:hidden}.icon-showcase .usage-examples .usage-example .code-block pre{margin:0;padding:16px;overflow-x:auto}.icon-showcase .usage-examples .usage-example .code-block pre code{color:#e0e0e0;font-family:Consolas,Courier New,monospace}.icon-showcase .example-scenarios{display:grid;grid-template-columns:repeat(auto-fill,minmax(350px,1fr));gap:24px}.icon-showcase .example-scenarios .scenario h3{font-size:18px;margin-bottom:16px}.icon-showcase .example-scenarios .scenario .bottom-nav-demo{display:flex;justify-content:space-between;background:var(--surface-color, #fff);border:1px solid var(--border-color, #e0e0e0);border-radius:8px;padding:12px}.icon-showcase .example-scenarios .scenario .bottom-nav-demo .nav-item{display:flex;flex-direction:column;align-items:center;padding:8px;min-width:60px}.icon-showcase .example-scenarios .scenario .bottom-nav-demo .nav-item .icon{margin-bottom:4px}.icon-showcase .example-scenarios .scenario .bottom-nav-demo .nav-item span{font-size:12px}.icon-showcase .example-scenarios .scenario .bottom-nav-demo .nav-item.active{color:var(--primary-color)}.icon-showcase .example-scenarios .scenario .action-buttons-demo{display:flex;gap:12px;flex-wrap:wrap}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn{display:flex;align-items:center;gap:8px;padding:8px 16px;border-radius:4px;border:none;background:#f5f5f5;cursor:pointer;transition:all .2s}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn:hover{background:#e0e0e0}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn.primary{background:var(--primary-color);color:#fff}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn.primary:hover{background:color-mix(in srgb,#5c6bc0,black 10%)}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn.danger{background:var(--error-color, #f44336);color:#fff}.icon-showcase .example-scenarios .scenario .action-buttons-demo .action-btn.danger:hover{background:color-mix(in srgb,#f44336,black 10%)}.icon-showcase .example-scenarios .scenario .data-card-demo{display:flex;gap:16px;flex-wrap:wrap}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card{flex:1;min-width:160px;border-radius:8px;overflow:hidden;border:1px solid var(--border-color, #e0e0e0);background:var(--surface-color, #fff)}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-header{display:flex;align-items:center;gap:8px;background:#00000008;padding:12px}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-header .card-icon{color:var(--primary-color)}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-header h4{margin:0;font-size:16px}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-body{padding:16px}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-body .data-value{font-size:24px;font-weight:700}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-body .data-value .unit{font-size:14px;font-weight:400;opacity:.7}.icon-showcase .example-scenarios .scenario .data-card-demo .data-card .card-body .data-desc{font-size:12px;color:var(--text-secondary, #666);margin-top:4px}.icon-showcase.theme-dark{background-color:#121212;color:#e0e0e0}.icon-showcase.theme-dark .showcase-header .description{color:#aaa}.icon-showcase.theme-dark .showcase-header .guide-link-container .guide-link{background:#222;border-color:#444;color:#bb86fc}.icon-showcase.theme-dark .showcase-header .guide-link-container .guide-link:hover{background:#333}.icon-showcase.theme-dark .showcase-header .guide-link-container .guide-link .icon{color:#bb86fc}.icon-showcase.theme-dark .showcase-header .search-container .search-input{background:#222;color:#e0e0e0;border-color:#444}.icon-showcase.theme-dark .showcase-header .search-container .search-input:focus{border-color:var(--primary-color)}.icon-showcase.theme-dark .icon-section h2{border-bottom-color:#444}.icon-showcase.theme-dark .icon-grid .icon-item{background:#222;border-color:#444}.icon-showcase.theme-dark .icon-grid .icon-item .icon,.icon-showcase.theme-dark .icon-grid .icon-item i{background:#333}.icon-showcase.theme-dark .icon-grid .icon-item .icon-label{color:#aaa}.icon-showcase.theme-dark .size-examples{background:#222;border-color:#444}.icon-showcase.theme-dark .size-examples .size-example .icon{background:#333}.icon-showcase.theme-dark .size-examples .size-example code{background:#333;color:#e0e0e0}.icon-showcase.theme-dark .usage-examples .usage-example .code-block{background:#000}.icon-showcase.theme-dark .example-scenarios .bottom-nav-demo{background:#222;border-color:#444}.icon-showcase.theme-dark .example-scenarios .action-buttons-demo .action-btn{background:#333;color:#e0e0e0}.icon-showcase.theme-dark .example-scenarios .action-buttons-demo .action-btn:hover{background:#444}.icon-showcase.theme-dark .example-scenarios .data-card-demo .data-card{background:#222;border-color:#444}.icon-showcase.theme-dark .example-scenarios .data-card-demo .data-card .card-header{background:#333}.icon-showcase.theme-dark .example-scenarios .data-card-demo .data-card .card-body .data-desc{color:#aaa}@media (max-width: 768px){.icon-showcase{padding:16px}.icon-showcase .showcase-header{margin-bottom:32px}.icon-showcase .showcase-header h1{font-size:28px}.icon-showcase .showcase-header .description{font-size:14px}.icon-showcase .showcase-header .search-container{max-width:100%}.icon-showcase .showcase-header .search-container .search-input{font-size:16px;padding:10px 14px}.icon-showcase .icon-section{margin-bottom:32px}.icon-showcase .icon-section h2{font-size:20px}.icon-showcase .icon-grid{grid-template-columns:repeat(auto-fill,minmax(100px,1fr));gap:12px}.icon-showcase .icon-grid.all-icons{grid-template-columns:repeat(auto-fill,minmax(80px,1fr))}.icon-showcase .icon-grid .icon-item{padding:12px 6px}.icon-showcase .icon-grid .icon-item .icon,.icon-showcase .icon-grid .icon-item i{font-size:20px;width:32px;height:32px;margin-bottom:8px}.icon-showcase .icon-grid .icon-item .icon-name,.icon-showcase .icon-grid .icon-item .icon-label{font-size:11px}.icon-showcase .size-examples{flex-direction:column;align-items:center;gap:16px;padding:16px}.icon-showcase .size-examples .size-example .icon{margin-bottom:8px;padding:8px}.icon-showcase .usage-examples,.icon-showcase .example-scenarios{grid-template-columns:1fr}}@media (max-width: 480px){.icon-showcase{padding:12px}.icon-showcase .showcase-header h1{font-size:24px}.icon-showcase .showcase-header .description{font-size:13px}.icon-showcase .icon-grid{grid-template-columns:repeat(auto-fill,minmax(80px,1fr));gap:8px}.icon-showcase .icon-grid.all-icons{grid-template-columns:repeat(auto-fill,minmax(70px,1fr))}.icon-showcase .icon-grid .icon-item{padding:8px 4px}.icon-showcase .icon-grid .icon-item .icon,.icon-showcase .icon-grid .icon-item i{font-size:18px;width:28px;height:28px;margin-bottom:6px}.icon-showcase .icon-grid .icon-item .icon-name,.icon-showcase .icon-grid .icon-item .icon-label{font-size:10px}}.icon-usage-guide{max-width:1200px;margin:0 auto;padding:24px;font-family:Arial,sans-serif}.icon-usage-guide .guide-header{margin-bottom:40px}.icon-usage-guide .guide-header h1{font-size:32px;margin-bottom:16px;color:var(--primary-color, #5c6bc0);border-left:4px solid var(--primary-color, #5c6bc0);padding-left:16px}.icon-usage-guide .guide-header .description{font-size:16px;line-height:1.6;color:var(--text-secondary, #666);max-width:800px}.icon-usage-guide .guide-section{margin-bottom:48px;background:var(--surface-color, #fff);border-radius:12px;padding:24px;box-shadow:0 2px 8px #0000001a}.icon-usage-guide .guide-section h2{font-size:24px;margin-bottom:16px;color:var(--text-primary, #333);border-bottom:2px solid var(--border-color, #eee);padding-bottom:8px}.icon-usage-guide .guide-section p{margin-bottom:20px;line-height:1.6;color:var(--text-secondary, #666)}.icon-usage-guide .category-box{margin-bottom:32px;border:1px solid var(--border-color, #eee);border-radius:8px;padding:20px;background-color:var(--surface-secondary, #f9fafb)}.icon-usage-guide .category-box h3{font-size:20px;margin-bottom:12px;display:flex;align-items:center;gap:8px;color:var(--text-primary, #333)}.icon-usage-guide .category-box h3 .icon{color:var(--primary-color, #5c6bc0)}.icon-usage-guide .category-box p{margin-bottom:16px}.icon-usage-guide .category-box .example-box{background:var(--surface-color, #fff);border-radius:8px;padding:16px}.icon-usage-guide .category-box .example-box h4{font-size:16px;margin-bottom:12px;color:var(--text-primary, #333)}.icon-usage-guide .bottom-nav-example{display:flex;background:var(--surface-color, #fff);border:1px solid var(--border-color, #eee);border-radius:24px;padding:12px 24px;margin-bottom:16px}.icon-usage-guide .bottom-nav-example .nav-item{display:flex;flex-direction:column;align-items:center;padding:8px 16px}.icon-usage-guide .bottom-nav-example .nav-item .icon{margin-bottom:4px;color:var(--text-secondary, #666)}.icon-usage-guide .bottom-nav-example .nav-item span{font-size:12px;color:var(--text-secondary, #666)}.icon-usage-guide .bottom-nav-example .nav-item.active .icon,.icon-usage-guide .bottom-nav-example .nav-item.active span{color:var(--primary-color, #5c6bc0)}.icon-usage-guide .action-buttons-example{display:flex;gap:12px;margin-bottom:16px}.icon-usage-guide .action-buttons-example .action-btn{display:flex;align-items:center;gap:8px;padding:10px 16px;border-radius:6px;border:none;cursor:pointer;font-size:14px;font-weight:500;transition:all .2s}.icon-usage-guide .action-buttons-example .action-btn.primary{background:var(--primary-color, #5c6bc0);color:#fff}.icon-usage-guide .action-buttons-example .action-btn.primary .icon{color:#fff}.icon-usage-guide .action-buttons-example .action-btn.primary:hover{background:color-mix(in srgb,#5c6bc0,black 10%)}.icon-usage-guide .action-buttons-example .action-btn.secondary{background:var(--surface-color, #fff);border:1px solid var(--border-color, #e0e0e0);color:var(--text-primary, #333)}.icon-usage-guide .action-buttons-example .action-btn.secondary:hover{background:var(--surface-secondary, #f5f5f5)}.icon-usage-guide .data-card-example{margin-bottom:16px}.icon-usage-guide .data-card-example .data-card{display:flex;align-items:center;gap:16px;background:var(--surface-secondary, #f9fafb);border-radius:12px;padding:16px;max-width:220px}.icon-usage-guide .data-card-example .data-card .icon{color:var(--primary-color, #5c6bc0);background:#5c6bc01a;padding:12px;border-radius:50%}.icon-usage-guide .data-card-example .data-card .card-content{display:flex;flex-direction:column}.icon-usage-guide .data-card-example .data-card .card-content .data-value{font-size:24px;font-weight:700;color:var(--text-primary, #333)}.icon-usage-guide .data-card-example .data-card .card-content .data-label{font-size:14px;color:var(--text-secondary, #666)}.icon-usage-guide .code-block{background:#1e1e1e;border-radius:8px;margin:16px 0;overflow:hidden}.icon-usage-guide .code-block pre{margin:0;padding:16px;overflow-x:auto}.icon-usage-guide .code-block pre code{color:#e0e0e0;font-family:Consolas,Courier New,monospace;line-height:1.5}.icon-usage-guide .sizes-table{margin:24px 0;overflow-x:auto}.icon-usage-guide .sizes-table table{width:100%;border-collapse:collapse}.icon-usage-guide .sizes-table table th,.icon-usage-guide .sizes-table table td{padding:12px;text-align:left;border-bottom:1px solid var(--border-color, #eee)}.icon-usage-guide .sizes-table table th{background:var(--surface-secondary, #f9fafb);color:var(--text-primary, #333);font-weight:600}.icon-usage-guide .sizes-table table td{vertical-align:middle}.icon-usage-guide .sizes-table table td code{background:var(--surface-secondary, #f9fafb);padding:2px 6px;border-radius:4px;font-family:Consolas,Courier New,monospace;font-size:14px}.icon-usage-guide .sizes-table table td .icon{display:flex;align-items:center;justify-content:center}.icon-usage-guide .colors-example{display:grid;grid-template-columns:repeat(auto-fill,minmax(140px,1fr));gap:16px;margin:24px 0}.icon-usage-guide .colors-example .color-item{display:flex;flex-direction:column;align-items:center;gap:8px}.icon-usage-guide .colors-example .color-item .icon-wrapper{width:48px;height:48px;display:flex;align-items:center;justify-content:center;border-radius:8px}.icon-usage-guide .colors-example .color-item .icon-wrapper.primary .icon{color:var(--primary-color, #5c6bc0)}.icon-usage-guide .colors-example .color-item .icon-wrapper.secondary .icon{color:var(--secondary-color, #757575)}.icon-usage-guide .colors-example .color-item .icon-wrapper.success .icon{color:var(--success-color, #4caf50)}.icon-usage-guide .colors-example .color-item .icon-wrapper.warning .icon{color:var(--warning-color, #ff9800)}.icon-usage-guide .colors-example .color-item .icon-wrapper.error .icon{color:var(--error-color, #f44336)}.icon-usage-guide .colors-example .color-item .icon-wrapper.disabled .icon{color:var(--disabled-color, #bdbdbd);opacity:.5}.icon-usage-guide .colors-example .color-item span{font-size:14px;color:var(--text-secondary, #666)}.icon-usage-guide .text-icon-examples{display:grid;grid-template-columns:repeat(auto-fill,minmax(300px,1fr));gap:24px;margin:24px 0}.icon-usage-guide .text-icon-examples .example h4{font-size:16px;margin-bottom:16px}.icon-usage-guide .text-icon-examples .example .example-item{display:flex;flex-direction:column;gap:24px}.icon-usage-guide .text-icon-examples .example .good-example,.icon-usage-guide .text-icon-examples .example .bad-example{padding:16px;border-radius:8px}.icon-usage-guide .text-icon-examples .example .good-example .note,.icon-usage-guide .text-icon-examples .example .bad-example .note{margin-top:12px;font-size:14px;color:var(--text-secondary, #666)}.icon-usage-guide .text-icon-examples .example .good-example{background:#4caf501a;border:1px dashed #4caf50}.icon-usage-guide .text-icon-examples .example .bad-example{background:#f443361a;border:1px dashed #f44336}.icon-usage-guide .text-icon-examples .example .btn{display:inline-flex;align-items:center;padding:8px 16px;border-radius:4px;border:none;background:var(--primary-color, #5c6bc0);color:#fff;font-size:14px;cursor:pointer}.icon-usage-guide .text-icon-examples .example .btn.with-icon .icon{margin-right:8px}.icon-usage-guide .text-icon-examples .example .btn.with-icon.bad-spacing .icon{margin-right:32px}.icon-usage-guide .text-icon-examples .example .btn.icon-only{padding:8px}.icon-usage-guide .text-icon-examples .example .aligned-item{display:flex;align-items:center;gap:8px}.icon-usage-guide .text-icon-examples .example .misaligned-item{display:flex;align-items:flex-start;gap:8px}.icon-usage-guide .text-icon-examples .example .misaligned-item .icon{margin-top:8px}.icon-usage-guide .usage-examples{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:24px;margin:24px 0}.icon-usage-guide .usage-examples .usage-example h3{font-size:18px;margin-bottom:16px;color:var(--text-primary, #333)}.icon-usage-guide .usage-examples .usage-example .note{margin-top:12px;font-size:14px;font-style:italic;color:var(--text-secondary, #666)}.icon-usage-guide .accessibility-list{list-style:none;padding:0;margin:24px 0}.icon-usage-guide .accessibility-list li{margin-bottom:24px}.icon-usage-guide .accessibility-list li .item-title{font-size:16px;font-weight:600;color:var(--text-primary, #333);display:block;margin-bottom:12px}.icon-usage-guide .accessibility-list li p{margin:12px 0}.icon-usage-guide .accessibility-list li .example-comparison{display:grid;grid-template-columns:repeat(auto-fill,minmax(200px,1fr));gap:16px;margin-top:16px}.icon-usage-guide.theme-dark{background-color:#121212;color:#e0e0e0}.icon-usage-guide.theme-dark .guide-header h1{color:#bb86fc;border-left-color:#bb86fc}.icon-usage-guide.theme-dark .guide-header .description{color:#bbb}.icon-usage-guide.theme-dark .guide-section{background:#1e1e1e;box-shadow:0 4px 12px #0000004d}.icon-usage-guide.theme-dark .guide-section h2{color:#e0e0e0;border-bottom-color:#333}.icon-usage-guide.theme-dark .guide-section p{color:#bbb}.icon-usage-guide.theme-dark .category-box{background-color:#262626;border-color:#333}.icon-usage-guide.theme-dark .category-box h3{color:#e0e0e0}.icon-usage-guide.theme-dark .category-box h3 .icon{color:#bb86fc}.icon-usage-guide.theme-dark .category-box .example-box{background:#1e1e1e}.icon-usage-guide.theme-dark .category-box .example-box h4{color:#e0e0e0}.icon-usage-guide.theme-dark .bottom-nav-example{background:#1e1e1e;border-color:#333}.icon-usage-guide.theme-dark .bottom-nav-example .nav-item .icon,.icon-usage-guide.theme-dark .bottom-nav-example .nav-item span{color:#bbb}.icon-usage-guide.theme-dark .bottom-nav-example .nav-item.active .icon,.icon-usage-guide.theme-dark .bottom-nav-example .nav-item.active span{color:#bb86fc}.icon-usage-guide.theme-dark .action-btn.primary{background:#bb86fc}.icon-usage-guide.theme-dark .action-btn.secondary{background:#333;border-color:#444;color:#e0e0e0}.icon-usage-guide.theme-dark .action-btn.secondary:hover{background:#444}.icon-usage-guide.theme-dark .data-card{background:#262626}.icon-usage-guide.theme-dark .data-card .icon{color:#bb86fc;background:#bb86fc1a}.icon-usage-guide.theme-dark .data-card .data-value{color:#e0e0e0}.icon-usage-guide.theme-dark .data-card .data-label{color:#bbb}.icon-usage-guide.theme-dark .code-block{background:#000}.icon-usage-guide.theme-dark .sizes-table table th{background:#262626;color:#e0e0e0}.icon-usage-guide.theme-dark .sizes-table table td{border-bottom-color:#333}.icon-usage-guide.theme-dark .sizes-table table td code{background:#333;color:#e0e0e0}.icon-usage-guide.theme-dark .good-example{background:#4caf500d}.icon-usage-guide.theme-dark .bad-example{background:#f443360d}.icon-usage-guide.theme-dark .usage-example h3,.icon-usage-guide.theme-dark .item-title{color:#e0e0e0}.icon-usage-guide.theme-dark .note{color:#bbb}@media (max-width: 768px){.icon-usage-guide{padding:16px}.icon-usage-guide .guide-header h1{font-size:24px}.icon-usage-guide .text-icon-examples,.icon-usage-guide .usage-examples{grid-template-columns:1fr}.icon-usage-guide .sizes-table table{min-width:600px}}.labeled-muscle-illustration{display:flex;flex-direction:column;align-items:center;gap:var(--space-4);padding:var(--space-4);background:var(--bg-surface);border-radius:var(--radius-lg);border:1px solid var(--border-color);-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none;-webkit-user-select:none;user-select:none}.labeled-muscle-illustration .muscle-legend{display:flex;flex-direction:column;align-items:center;gap:var(--space-2);margin-bottom:var(--space-4)}.labeled-muscle-illustration .muscle-legend h3{font-size:var(--text-lg);font-weight:var(--font-semibold);color:var(--text-primary);margin:0}.labeled-muscle-illustration .muscle-legend .legend-items{display:flex;gap:var(--space-6)}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item{display:flex;align-items:center;gap:var(--space-2)}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item .legend-color{width:20px;height:20px;border-radius:var(--radius-sm);border:1px solid var(--border-color)}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item span{font-size:var(--text-base);color:var(--text-primary);font-weight:var(--font-medium)}.labeled-muscle-illustration .muscle-body-container{display:flex;justify-content:center;align-items:flex-start;width:100%;max-width:800px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper{position:relative;width:100%;max-width:600px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-illustration-labeled{width:100%;height:auto;display:block;filter:drop-shadow(0 2px 4px rgba(0,0,0,.1));will-change:transform;transform:translateZ(0);backfaceVisibility:hidden;shape-rendering:optimizeSpeed;image-rendering:optimizeSpeed}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay{position:absolute;top:0;left:0;width:100%;height:100%;pointer-events:none;z-index:10}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{position:absolute;padding:4px 8px;border-radius:4px;font-size:12px;font-weight:500;white-space:nowrap;transition:all .2s ease;background:#ffffffe6;color:#374151;border:1px solid #d1d5db;-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px);box-shadow:0 2px 4px #0000001a}@media (max-width: 768px){.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{font-size:11px;padding:3px 6px}}@media (max-width: 480px){.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{font-size:10px;padding:2px 5px}}@supports (backdrop-filter: blur(4px)){.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{-webkit-backdrop-filter:blur(4px);backdrop-filter:blur(4px)}}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label:before{content:"";position:absolute;top:50%;left:-6px;width:6px;height:1px;background:currentColor;opacity:.4;transform:translateY(-50%)}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label[data-view=back]:before{left:auto;right:-6px}.labeled-muscle-illustration-loading{display:flex;justify-content:center;align-items:center;min-height:400px}.labeled-muscle-illustration-loading .animate-spin{animation:spin 1s linear infinite}@keyframes spin{0%{transform:rotate(0)}to{transform:rotate(360deg)}}@media (max-width: 768px){.labeled-muscle-illustration{padding:var(--space-3);gap:var(--space-3)}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper{max-width:500px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{font-size:11px;padding:3px 6px;border-radius:3px}.labeled-muscle-illustration .muscle-legend{margin-bottom:var(--space-3)}.labeled-muscle-illustration .muscle-legend .legend-items{gap:var(--space-4)}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item .legend-color{width:16px;height:16px}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item span{font-size:var(--text-sm)}}@media (max-width: 480px){.labeled-muscle-illustration{padding:var(--space-2);gap:var(--space-2)}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper{max-width:400px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{font-size:10px;padding:2px 5px;border-radius:3px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label:before{width:4px;left:-4px}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label[data-view=back]:before{right:-4px}.labeled-muscle-illustration .muscle-legend h3{font-size:var(--text-base)}.labeled-muscle-illustration .muscle-legend .legend-items{gap:var(--space-3)}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item .legend-color{width:14px;height:14px}.labeled-muscle-illustration .muscle-legend .legend-items .legend-item span{font-size:var(--text-xs)}}.theme-dark .labeled-muscle-illustration .muscle-legend h3,.theme-dark .labeled-muscle-illustration .muscle-legend .legend-items .legend-item span{color:var(--text-primary)}.theme-dark .labeled-muscle-illustration .muscle-labels-overlay .muscle-label{background:#000c;color:#f9fafb;border-color:#fff3;-webkit-backdrop-filter:blur(8px);backdrop-filter:blur(8px);box-shadow:0 2px 8px #0000004d}@supports (-webkit-touch-callout: none){.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper{-webkit-overflow-scrolling:touch}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-illustration-labeled{-webkit-transform:translateZ(0);-webkit-backface-visibility:hidden;-webkit-perspective:1000}.labeled-muscle-illustration .muscle-body-container .muscle-svg-wrapper .muscle-labels-overlay .muscle-label{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}}.muscle-demo-page{min-height:100vh;background:var(--bg-primary);color:var(--text-primary);padding:var(--space-4);font-family:-apple-system,BlinkMacSystemFont,Segoe UI,system-ui,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-tap-highlight-color:transparent}.muscle-demo-page .demo-header{text-align:center;margin-bottom:var(--space-8)}.muscle-demo-page .demo-header h1{font-size:var(--text-3xl);font-weight:var(--font-bold);margin-bottom:var(--space-2);color:var(--text-primary)}.muscle-demo-page .demo-header p{font-size:var(--text-lg);color:var(--text-secondary);margin:0}.muscle-demo-page .demo-controls{display:flex;flex-wrap:wrap;gap:var(--space-6);justify-content:center;margin-bottom:var(--space-6);padding:var(--space-4);background:var(--bg-secondary);border-radius:var(--radius-lg);border:1px solid var(--border-color)}.muscle-demo-page .demo-controls .control-group{display:flex;flex-direction:column;align-items:center;gap:var(--space-2)}.muscle-demo-page .demo-controls .control-group label{font-size:var(--text-sm);font-weight:var(--font-medium);color:var(--text-secondary)}.muscle-demo-page .demo-controls .control-group .toggle-buttons{display:flex;gap:var(--space-1)}.muscle-demo-page .demo-controls .control-group .toggle-buttons button{padding:var(--space-2) var(--space-3);border:1px solid var(--border-color);background:var(--bg-primary);color:var(--text-primary);border-radius:var(--radius-md);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all .2s ease;min-height:var(--ios-touch-target)}.muscle-demo-page .demo-controls .control-group .toggle-buttons button:hover{background:var(--bg-hover)}.muscle-demo-page .demo-controls .control-group .toggle-buttons button.active{background:var(--color-primary);color:#fff;border-color:var(--color-primary)}.muscle-demo-page .demo-controls .control-group .toggle-buttons button:active{transform:scale(.98)}.muscle-demo-page .demo-controls .control-group .preset-buttons{display:flex;flex-wrap:wrap;gap:var(--space-2);justify-content:center}.muscle-demo-page .demo-controls .control-group .preset-buttons button{padding:var(--space-2) var(--space-3);border:1px solid var(--border-color);background:var(--bg-primary);color:var(--text-primary);border-radius:var(--radius-md);font-size:var(--text-sm);cursor:pointer;transition:all .2s ease;min-height:var(--ios-touch-target)}.muscle-demo-page .demo-controls .control-group .preset-buttons button:hover{background:var(--color-primary);color:#fff;border-color:var(--color-primary)}.muscle-demo-page .demo-controls .control-group .preset-buttons button:active{transform:scale(.98)}.muscle-demo-page .selected-muscles-info{text-align:center;margin-bottom:var(--space-6)}.muscle-demo-page .selected-muscles-info h3{font-size:var(--text-xl);font-weight:var(--font-semibold);margin-bottom:var(--space-3);color:var(--text-primary)}.muscle-demo-page .selected-muscles-info .muscle-tags{display:flex;flex-wrap:wrap;gap:var(--space-2);justify-content:center}.muscle-demo-page .selected-muscles-info .muscle-tags .muscle-tag{padding:var(--space-1) var(--space-2);background:var(--color-primary);color:#fff;border-radius:var(--radius-sm);font-size:var(--text-sm);font-weight:var(--font-medium);cursor:pointer;transition:all .2s ease}.muscle-demo-page .selected-muscles-info .muscle-tags .muscle-tag:hover{background:var(--color-primary-dark);transform:scale(1.05)}.muscle-demo-page .selected-muscles-info .muscle-tags .muscle-tag:active{transform:scale(.95)}.muscle-demo-page .demo-visualization{display:flex;justify-content:center;margin-bottom:var(--space-8)}.muscle-demo-page .demo-visualization>*{max-width:800px;width:100%}.muscle-demo-page .demo-features .features-grid{display:grid;grid-template-columns:repeat(auto-fit,minmax(280px,1fr));gap:var(--space-4);margin-top:var(--space-4)}.muscle-demo-page .demo-features .features-grid .feature-card{padding:var(--space-4);background:var(--bg-secondary);border-radius:var(--radius-lg);border:1px solid var(--border-color);text-align:center;transition:transform .2s ease}.muscle-demo-page .demo-features .features-grid .feature-card:hover{transform:translateY(-2px);box-shadow:0 4px 12px #0000001a}.muscle-demo-page .demo-features .features-grid .feature-card h4{font-size:var(--text-lg);font-weight:var(--font-semibold);margin-bottom:var(--space-2);color:var(--text-primary)}.muscle-demo-page .demo-features .features-grid .feature-card p{font-size:var(--text-sm);color:var(--text-secondary);line-height:1.5;margin:0}.muscle-demo-page .demo-features h3{font-size:var(--text-2xl);font-weight:var(--font-bold);text-align:center;margin-bottom:var(--space-4);color:var(--text-primary)}@media (max-width: 768px){.muscle-demo-page{padding:var(--space-3)}.muscle-demo-page .demo-header{margin-bottom:var(--space-6)}.muscle-demo-page .demo-header h1{font-size:var(--text-2xl)}.muscle-demo-page .demo-header p{font-size:var(--text-base)}.muscle-demo-page .demo-controls{flex-direction:column;gap:var(--space-4)}.muscle-demo-page .demo-controls .control-group .preset-buttons button{font-size:var(--text-xs);padding:var(--space-1) var(--space-2)}.muscle-demo-page .selected-muscles-info h3{font-size:var(--text-lg)}.muscle-demo-page .selected-muscles-info .muscle-tags .muscle-tag{font-size:var(--text-xs)}.muscle-demo-page .demo-features .features-grid{grid-template-columns:1fr;gap:var(--space-3)}.muscle-demo-page .demo-features h3{font-size:var(--text-xl)}}@media (max-width: 480px){.muscle-demo-page{padding:var(--space-2)}.muscle-demo-page .demo-header{margin-bottom:var(--space-4)}.muscle-demo-page .demo-header h1{font-size:var(--text-xl)}.muscle-demo-page .demo-header p{font-size:var(--text-sm)}.muscle-demo-page .demo-controls{padding:var(--space-3);gap:var(--space-3)}.muscle-demo-page .selected-muscles-info{margin-bottom:var(--space-4)}.muscle-demo-page .demo-visualization{margin-bottom:var(--space-6)}}.theme-dark{background:var(--bg-primary)}.theme-dark .demo-controls,.theme-dark .demo-features .feature-card{background:var(--bg-secondary);border-color:var(--border-color)}.theme-dark .demo-features .feature-card:hover{box-shadow:0 4px 12px #0000004d}@supports (-webkit-touch-callout: none){.muscle-demo-page{-webkit-overflow-scrolling:touch}.muscle-demo-page .demo-controls .control-group .toggle-buttons button,.muscle-demo-page .demo-controls .control-group .preset-buttons button,.muscle-demo-page .selected-muscles-info .muscle-tags .muscle-tag{-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none}}.exercise-muscle-card{display:flex;background:var(--bg-primary);border:1px solid var(--border-color, #e5e7eb);border-radius:12px;overflow:hidden;box-shadow:0 2px 8px #0000000f;transition:all .2s ease}@supports (-webkit-touch-callout: none){.exercise-muscle-card{-webkit-transform:translateZ(0);-webkit-backface-visibility:hidden}}.exercise-muscle-card.light{--bg-primary: #ffffff;--bg-secondary: #f8fafc;--text-primary: #1a1a1a;--text-secondary: #6b7280;--border-color: #e5e7eb;--card-shadow: 0 2px 8px rgba(0, 0, 0, .06)}.exercise-muscle-card.dark{--bg-primary: #1e293b;--bg-secondary: #334155;--text-primary: #f8fafc;--text-secondary: #cbd5e1;--border-color: #475569;--card-shadow: 0 2px 8px rgba(0, 0, 0, .2)}.exercise-muscle-card.sm{min-height:180px}.exercise-muscle-card.sm .muscle-text-section{flex:0 0 120px;padding:12px}.exercise-muscle-card.sm .muscle-visual-section .muscle-illustration-container{max-width:140px}.exercise-muscle-card.sm .muscle-category{font-size:12px;margin-bottom:6px}.exercise-muscle-card.sm .muscle-tag{font-size:10px;padding:2px 6px}.exercise-muscle-card.md{min-height:240px}.exercise-muscle-card.md .muscle-text-section{flex:0 0 160px;padding:16px}.exercise-muscle-card.md .muscle-visual-section .muscle-illustration-container{max-width:200px}.exercise-muscle-card.lg{min-height:300px}.exercise-muscle-card.lg .muscle-text-section{flex:0 0 200px;padding:20px}.exercise-muscle-card.lg .muscle-visual-section .muscle-illustration-container{max-width:280px}.exercise-muscle-card.lg .muscle-category{font-size:16px;margin-bottom:12px}.exercise-muscle-card.lg .muscle-tag{font-size:14px;padding:6px 12px}.exercise-muscle-card .muscle-text-section{flex:0 0 160px;background:var(--bg-secondary);border-right:1px solid var(--border-color);display:flex;align-items:center;padding:16px}.exercise-muscle-card .muscle-text-section .muscle-text-content{width:100%}.exercise-muscle-card .muscle-text-section .muscle-group:not(:last-child){margin-bottom:16px}.exercise-muscle-card .muscle-text-section .muscle-category{font-size:14px;font-weight:600;color:var(--text-primary);margin:0 0 8px;letter-spacing:.025em;position:relative;padding-bottom:6px;display:inline-block;width:auto;background-image:linear-gradient(transparent,transparent);background-repeat:no-repeat;background-size:100% 5px;background-position:0 calc(100% - 2px)}.exercise-muscle-card .muscle-text-section .muscle-group:has(.muscle-tag.primary) .muscle-category{background-image:linear-gradient(#3b82f6,#3b82f6)}.exercise-muscle-card .muscle-text-section .muscle-group:has(.muscle-tag.secondary) .muscle-category{background-image:linear-gradient(#93c5fd,#93c5fd)}.exercise-muscle-card .muscle-text-section .muscle-list{display:flex;flex-direction:column;gap:4px}.exercise-muscle-card .muscle-text-section .muscle-tag{display:inline-block;font-size:12px;font-weight:500;padding:6px 12px;border-radius:20px;line-height:1.2;text-align:left;max-width:fit-content;white-space:nowrap;background:#f3f4f6;color:#374151;transition:all .2s ease}.exercise-muscle-card .muscle-text-section .muscle-tag:hover{background:#e5e7eb;transform:translateY(-1px)}.exercise-muscle-card .muscle-text-section .muscle-tag.primary,.exercise-muscle-card .muscle-text-section .muscle-tag.secondary{background:#f3f4f6;color:#374151}.exercise-muscle-card .muscle-text-section .empty-state{display:flex;align-items:center;justify-content:center;height:100%;padding:20px}.exercise-muscle-card .muscle-text-section .empty-state .empty-text{color:var(--text-secondary);font-size:12px;text-align:center}.exercise-muscle-card .muscle-visual-section{flex:1;display:flex;align-items:center;justify-content:center;padding:20px;background:var(--bg-primary)}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container{width:100%;max-width:200px;height:auto}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=CHEST] .muscle-illustration .muscle-selected[data-muscle*=chest] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=BICEPS] .muscle-illustration .muscle-selected[data-muscle*=biceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=SHOULDERS] .muscle-illustration .muscle-selected[data-muscle*=shoulder] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=BACK] .muscle-illustration .muscle-selected[data-muscle*=back] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=TRICEPS] .muscle-illustration .muscle-selected[data-muscle*=triceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=QUADRICEPS] .muscle-illustration .muscle-selected[data-muscle*=quadriceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=HAMSTRINGS] .muscle-illustration .muscle-selected[data-muscle*=hamstrings] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=GLUTES] .muscle-illustration .muscle-selected[data-muscle*=glutes] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=ABDOMINALS] .muscle-illustration .muscle-selected[data-muscle*=abs] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-primary-muscles*=CALVES] .muscle-illustration .muscle-selected[data-muscle*=calves] path{fill:#3b82f6!important}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=CHEST] .muscle-illustration .muscle-selected[data-muscle*=chest] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=BICEPS] .muscle-illustration .muscle-selected[data-muscle*=biceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=SHOULDERS] .muscle-illustration .muscle-selected[data-muscle*=shoulder] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=BACK] .muscle-illustration .muscle-selected[data-muscle*=back] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=TRICEPS] .muscle-illustration .muscle-selected[data-muscle*=triceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=QUADRICEPS] .muscle-illustration .muscle-selected[data-muscle*=quadriceps] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=HAMSTRINGS] .muscle-illustration .muscle-selected[data-muscle*=hamstrings] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=GLUTES] .muscle-illustration .muscle-selected[data-muscle*=glutes] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=ABDOMINALS] .muscle-illustration .muscle-selected[data-muscle*=abs] path,.exercise-muscle-card .muscle-visual-section .muscle-illustration-container[data-secondary-muscles*=CALVES] .muscle-illustration .muscle-selected[data-muscle*=calves] path{fill:#93c5fd!important}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container .muscle-illustration .muscle-selected path{fill:#3b82f6}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container .muscle-illustration{will-change:transform;transform:translateZ(0);-webkit-backface-visibility:hidden}@media (max-width: 768px){.exercise-muscle-card{flex-direction:column;min-height:auto}.exercise-muscle-card .muscle-text-section{flex:none;border-right:none;border-bottom:1px solid var(--border-color);padding:12px}.exercise-muscle-card .muscle-text-section .muscle-list{flex-direction:row;flex-wrap:wrap;gap:6px}.exercise-muscle-card .muscle-visual-section{padding:16px}.exercise-muscle-card .muscle-visual-section .muscle-illustration-container{max-width:160px}.exercise-muscle-card.sm .muscle-text-section{padding:8px}.exercise-muscle-card.lg .muscle-text-section{padding:16px}}.exercise-muscle-card.dark{box-shadow:var(--card-shadow)}.exercise-muscle-card.dark .muscle-text-section .muscle-tag.primary{background:#2563eb;border-color:#2563eb}.exercise-muscle-card.dark .muscle-text-section .muscle-tag.secondary{background:#7c3aed;color:#c4b5fd;border-color:#7c3aed}@media (hover: hover){.exercise-muscle-card:hover{transform:translateY(-1px);box-shadow:0 4px 12px #0000001a}.exercise-muscle-card:hover.dark{box-shadow:0 4px 12px #0000004d}}.exercise-muscle-card:focus-within{outline:2px solid #3b82f6;outline-offset:2px}.muscle-selector{font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,sans-serif}#muscle-illustration{max-width:100%;height:auto;-webkit-user-select:none;user-select:none}.muscle-group{cursor:pointer;transition:all .1s ease-out}.muscle-default{fill:#94a3b8}.muscle-hover:hover{fill:#93c5fd}.muscle-selected{fill:#3b82f6}@media (max-width: 768px){#muscle-illustration{max-width:90%}}@media (max-width: 480px){#muscle-illustration{max-width:100%}}@media (prefers-color-scheme: dark){.muscle-selector{color:#f1f5f9}.muscle-default{fill:#64748b}}.enhanced-muscle-illustration,.muscle-visualization-module.enhanced .muscle-illustration{max-width:100%;height:auto;-webkit-user-select:none;user-select:none;will-change:transform;transform:translateZ(0);backface-visibility:hidden}.enhanced-muscle-illustration .muscle-group,.muscle-visualization-module.enhanced .muscle-illustration .muscle-group{cursor:pointer;transition:all .1s ease-out}.enhanced-muscle-illustration .muscle-group:hover .muscle-default,.muscle-visualization-module.enhanced .muscle-illustration .muscle-group:hover .muscle-default,.enhanced-muscle-illustration .muscle-group:hover .fill-slate-400,.muscle-visualization-module.enhanced .muscle-illustration .muscle-group:hover .fill-slate-400{@apply fill-blue-400;}.enhanced-muscle-illustration .muscle-group:active,.muscle-visualization-module.enhanced .muscle-illustration .muscle-group:active{transform:scale(.98)}.enhanced-muscle-illustration .muscle-path,.muscle-visualization-module.enhanced .muscle-illustration .muscle-path{vector-effect:non-scaling-stroke;transition:fill .1s ease-out,stroke .1s ease-out}.enhanced-muscle-illustration .fill-transparent,.muscle-visualization-module.enhanced .muscle-illustration .fill-transparent{fill:transparent}.theme-light #enhanced-muscle-illustration .muscle-default{@apply fill-slate-400;}.theme-light #enhanced-muscle-illustration .muscle-selected{@apply fill-blue-500;}.theme-light #enhanced-muscle-illustration .muscle-hover:hover{@apply fill-blue-400;}.theme-dark #enhanced-muscle-illustration .muscle-default{@apply fill-sky-300 fill-opacity-60;}.theme-dark #enhanced-muscle-illustration .muscle-selected{@apply fill-sky-400;}.theme-dark #enhanced-muscle-illustration .muscle-hover:hover{@apply fill-sky-400 fill-opacity-80;}@media (max-width: 768px){.enhanced-muscle-illustration,.muscle-visualization-module.enhanced .muscle-illustration{max-width:95%}.enhanced-muscle-illustration .muscle-group,.muscle-visualization-module.enhanced .muscle-illustration .muscle-group{cursor:pointer;-webkit-tap-highlight-color:transparent}}@media (max-width: 480px){.enhanced-muscle-illustration,.muscle-visualization-module.enhanced .muscle-illustration{max-width:100%}.enhanced-muscle-illustration .muscle-path,.muscle-visualization-module.enhanced .muscle-illustration .muscle-path{stroke-width:1.2}}.muscle-visualization-loading{display:flex;align-items:center;justify-content:center;min-height:200px}.muscle-visualization-loading .loading-spinner{@apply animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500;}.muscle-visualization-optimized{will-change:transform;transform:translateZ(0);backface-visibility:hidden;transform-style:preserve-3d}.muscle-visualization-optimized .muscle-group{will-change:fill,transform;transform:translateZ(0)}.muscle-visualization-accessible .muscle-group:focus{outline:2px solid #3b82f6;outline-offset:2px}@media (prefers-contrast: high){.muscle-visualization-accessible .muscle-default{@apply fill-gray-700;}.muscle-visualization-accessible .muscle-selected{@apply fill-blue-700;}}@media (prefers-reduced-motion: reduce){.muscle-visualization-accessible .muscle-group,.muscle-visualization-accessible .muscle-path{transition:none}}.muscle-visualization-module.transitioning .muscle-illustration{opacity:.7;transition:opacity .3s ease-in-out}.muscle-visualization-module.transitioning.transition-complete .muscle-illustration{opacity:1}.muscle-visualization-debug{border:2px dashed #ef4444;position:relative}.muscle-visualization-debug:before{content:"🔧 Enhanced Muscle Visualization (Debug Mode)";position:absolute;top:-30px;left:0;background:#ef4444;color:#fff;padding:4px 8px;font-size:12px;border-radius:4px;z-index:1000}.muscle-visualization-debug .muscle-group{outline:1px solid rgba(239,68,68,.3)}.muscle-visualization-debug .muscle-group:hover{outline-color:#ef4444cc}.muscle-visualization-size-sm .enhanced-muscle-illustration,.muscle-visualization-size-sm .muscle-visualization-module.enhanced .muscle-illustration,.muscle-visualization-module.enhanced .muscle-visualization-size-sm .muscle-illustration{max-width:300px}.muscle-visualization-size-sm .muscle-path{stroke-width:.8}.muscle-visualization-size-md .enhanced-muscle-illustration,.muscle-visualization-size-md .muscle-visualization-module.enhanced .muscle-illustration,.muscle-visualization-module.enhanced .muscle-visualization-size-md .muscle-illustration{max-width:500px}.muscle-visualization-size-md .muscle-path{stroke-width:1}.muscle-visualization-size-lg .enhanced-muscle-illustration,.muscle-visualization-size-lg .muscle-visualization-module.enhanced .muscle-illustration,.muscle-visualization-module.enhanced .muscle-visualization-size-lg .muscle-illustration{max-width:700px}.muscle-visualization-size-lg .muscle-path{stroke-width:1.2}:root{--bg-primary: #ffffff;--bg-secondary: #f9fafb;--bg-tertiary: #f3f4f6;--bg-quaternary: #d1d5db;--bg-surface: #ffffff;--bg-card: #f9fafb;--bg-hover: #f3f4f6;--text-primary: #1f2937;--text-secondary: #4b5563;--text-tertiary: #6b7280;--text-disabled: #9ca3af;--text-on-accent: #ffffff;--border-color: #e5e7eb;--border-light: #f3f4f6;--border-heavy: #d1d5db;--accent-500: #3b82f6;--accent-600: #2563eb;--accent-400: #60a5fa;--accent-300: #93c5fd;--primary-400: #94a3b8;--primary-500: #64748b;--primary-600: #475569;--primary-700: #334155;--success-500: #22c55e;--warning-500: #eab308;--error-500: #ef4444;--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05)}.theme-dark{--bg-primary: #0f172a;--bg-secondary: #1e293b;--bg-tertiary: #334155;--bg-surface: #1e293b;--bg-card: #334155;--bg-hover: #475569;--text-primary: #f8fafc;--text-secondary: #cbd5e1;--text-tertiary: #94a3b8;--text-disabled: #64748b;--text-on-accent: #ffffff;--border-color: #374151;--border-light: #4b5563;--border-heavy: #6b7280;--accent-500: #3b82f6;--accent-600: #2563eb;--accent-400: #60a5fa;--accent-300: #93c5fd;--primary-400: #64748b;--primary-500: #475569;--primary-600: #334155;--primary-700: #1e293b;--success-500: #22c55e;--warning-500: #eab308;--error-500: #ef4444;--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .3);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .4), 0 2px 4px -1px rgba(0, 0, 0, .3);--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .4), 0 4px 6px -2px rgba(0, 0, 0, .3)}:root,.theme-dark{--nav-bg: var(--bg-surface);--nav-text: var(--text-secondary);--nav-text-active: var(--accent-500);--nav-border: var(--border-color);--card-bg: var(--bg-surface);--card-border: var(--border-color);--card-shadow: var(--shadow-md);--btn-primary-bg: var(--accent-500);--btn-primary-text: #ffffff;--btn-primary-hover: var(--accent-600);--input-bg: var(--bg-surface);--input-border: var(--border-color);--input-text: var(--text-primary);--input-placeholder: var(--text-tertiary);--selected-bg: var(--accent-500);--selected-text: #ffffff;--selected-border: var(--accent-500)}.ios-safe-area{padding-top:env(safe-area-inset-top);padding-bottom:env(safe-area-inset-bottom);padding-left:env(safe-area-inset-left);padding-right:env(safe-area-inset-right)}.ios-safe-area-top{padding-top:env(safe-area-inset-top)}.ios-safe-area-bottom{padding-bottom:env(safe-area-inset-bottom)}.theme-transition{transition:background-color .3s ease,color .3s ease,border-color .3s ease,box-shadow .3s ease}@media (prefers-contrast: high){:root{--text-primary: #000000;--border-color: #000000;--accent-500: #0066cc}.theme-dark{--text-primary: #ffffff;--border-color: #ffffff;--accent-500: #66b3ff}}@media (prefers-reduced-motion: reduce){.theme-transition{transition:none}}:root{--primary-900: #1a1a2e;--primary-800: #16213e;--primary-700: #0f172a;--primary-600: #1e293b;--primary-500: #334155;--primary-400: #475569;--primary-300: #64748b;--primary-200: #94a3b8;--primary-100: #cbd5e1;--accent-500: #3b82f6;--accent-400: #60a5fa;--accent-300: #93c5fd;--accent-200: #dbeafe;--accent-100: #eff6ff;--success-500: #22c55e;--success-400: #4ade80;--success-300: #86efac;--success-200: #bbf7d0;--success-100: #dcfce7;--warning-500: #f59e0b;--warning-400: #fbbf24;--warning-300: #fcd34d;--warning-200: #fde68a;--warning-100: #fef3c7;--error-500: #ef4444;--error-400: #f87171;--error-300: #fca5a5;--error-200: #fecaca;--error-100: #fee2e2;--text-primary: #f8fafc;--text-secondary: #cbd5e1;--text-tertiary: #94a3b8;--text-disabled: #64748b;--text-on-primary: #ffffff;--text-on-accent: #ffffff;--text-on-success: #ffffff;--text-on-warning: #ffffff;--text-on-error: #ffffff;--bg-primary: #0f172a;--bg-secondary: #1e293b;--bg-tertiary: #334155;--bg-surface: #1e293b;--bg-card: #374151;--bg-hover: #4b5563;--bg-overlay: rgba(15, 23, 42, .8);--border-color: #374151;--border-light: #f3f4f6;--color-primary: #3b82f6;--color-success: #22c55e;--color-warning: #f59e0b;--color-danger: #ef4444;--font-primary: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", sans-serif;--font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;--font-display: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;--text-xs: .75rem;--text-sm: .875rem;--text-base: 1rem;--text-lg: 1.125rem;--text-xl: 1.25rem;--text-2xl: 1.5rem;--text-3xl: 1.875rem;--text-4xl: 2.25rem;--text-5xl: 3rem;--text-6xl: 3.75rem;--font-light: 300;--font-normal: 400;--font-medium: 500;--font-semibold: 600;--font-bold: 700;--font-extrabold: 800;--font-black: 900;--leading-none: 1;--leading-tight: 1.25;--leading-snug: 1.375;--leading-normal: 1.5;--leading-relaxed: 1.625;--leading-loose: 2;--space-0: 0;--space-1: .25rem;--space-2: .5rem;--space-3: .75rem;--space-4: 1rem;--space-5: 1.25rem;--space-6: 1.5rem;--space-7: 1.75rem;--space-8: 2rem;--space-9: 2.25rem;--space-10: 2.5rem;--space-11: 2.75rem;--space-12: 3rem;--space-14: 3.5rem;--space-16: 4rem;--space-20: 5rem;--space-24: 6rem;--space-28: 7rem;--space-32: 8rem;--radius-none: 0;--radius-sm: .125rem;--radius-md: .375rem;--radius-lg: .5rem;--radius-xl: .75rem;--radius-2xl: 1rem;--radius-3xl: 1.5rem;--radius-full: 9999px;--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .05);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -2px rgba(0, 0, 0, .05);--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 10px 10px -5px rgba(0, 0, 0, .04);--shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, .25);--shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, .06);--transition-fast: .15s;--transition-normal: .2s;--transition-slow: .3s;--transition-slower: .5s;--ease-linear: linear;--ease-in: cubic-bezier(.4, 0, 1, 1);--ease-out: cubic-bezier(0, 0, .2, 1);--ease-in-out: cubic-bezier(.4, 0, .2, 1);--ease-bounce: cubic-bezier(.68, -.55, .265, 1.55);--container-xs: 20rem;--container-sm: 24rem;--container-md: 28rem;--container-lg: 32rem;--container-xl: 36rem;--container-2xl: 42rem;--container-3xl: 48rem;--container-4xl: 56rem;--container-5xl: 64rem;--container-6xl: 72rem;--container-7xl: 80rem;--container-full: 100%;--sidebar-width: 16rem;--sidebar-width-collapsed: 4rem;--header-height: 4rem;--header-height-mobile: 3.5rem;--breakpoint-sm: 640px;--breakpoint-md: 768px;--breakpoint-lg: 1024px;--breakpoint-xl: 1280px;--breakpoint-2xl: 1536px;--z-dropdown: 1000;--z-sticky: 1020;--z-fixed: 1030;--z-modal-backdrop: 1040;--z-modal: 1050;--z-popover: 1060;--z-tooltip: 1070;--z-toast: 1080;--progress-bg: var(--primary-600);--progress-calories: var(--error-500);--progress-exercise: var(--accent-500);--progress-move: var(--success-500);--rarity-common: var(--text-tertiary);--rarity-rare: var(--accent-500);--rarity-epic: var(--warning-500);--rarity-legendary: linear-gradient(45deg, var(--warning-500), var(--error-500));--status-completed: var(--success-500);--status-in-progress: var(--accent-500);--status-planned: var(--warning-500);--status-skipped: var(--text-tertiary);--chart-primary: var(--accent-500);--chart-secondary: var(--success-500);--chart-tertiary: var(--warning-500);--chart-quaternary: var(--error-500);--chart-quinary: var(--primary-300);--chart-grid: var(--primary-500);--chart-text: var(--text-secondary);--chart-axis: var(--text-tertiary);--focus-ring: 0 0 0 2px var(--accent-500);--focus-ring-offset: 2px;--selection-bg: rgba(59, 130, 246, .2);--selection-color: var(--text-primary);--glass-bg: rgba(30, 41, 59, .8);--glass-border: rgba(255, 255, 255, .1);--glass-blur: blur(10px);--gradient-primary: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-400) 100%);--gradient-surface: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%);--gradient-success: linear-gradient(135deg, var(--success-500) 0%, var(--success-400) 100%);--gradient-warning: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-400) 100%);--gradient-error: linear-gradient(135deg, var(--error-500) 0%, var(--error-400) 100%);--gradient-brand: linear-gradient(90deg, var(--accent-500), var(--success-500))}.theme-light{--primary-900: #f8fafc;--primary-800: #f1f5f9;--primary-700: #ffffff;--primary-600: #f8fafc;--primary-500: #e2e8f0;--primary-400: #cbd5e1;--primary-300: #94a3b8;--primary-200: #64748b;--primary-100: #475569;--text-primary: #1e293b;--text-secondary: #475569;--text-tertiary: #64748b;--text-disabled: #94a3b8;--text-on-primary: #ffffff;--text-on-accent: #ffffff;--text-on-success: #ffffff;--text-on-warning: #ffffff;--text-on-error: #ffffff;--bg-primary: #ffffff;--bg-secondary: #f8fafc;--bg-tertiary: #f1f5f9;--bg-surface: #ffffff;--bg-card: #f8fafc;--bg-hover: #f1f5f9;--bg-overlay: rgba(0, 0, 0, .1);--border-color: #e2e8f0;--border-light: #f3f4f6;--color-primary: #3b82f6;--color-success: #22c55e;--color-warning: #f59e0b;--color-danger: #ef4444;--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, .1);--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, .15), 0 2px 4px -1px rgba(0, 0, 0, .1);--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, .15), 0 4px 6px -2px rgba(0, 0, 0, .1);--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, .15), 0 10px 10px -5px rgba(0, 0, 0, .08);--shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, .3);--shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, .1);--selection-bg: rgba(59, 130, 246, .15);--selection-color: var(--text-primary);--glass-bg: rgba(255, 255, 255, .8);--glass-border: rgba(0, 0, 0, .1);--gradient-surface: linear-gradient(135deg, var(--bg-surface) 0%, var(--primary-600) 100%)}*{box-sizing:border-box}html{scroll-behavior:smooth}body{margin:0;padding:0;font-family:var(--font-primary);font-size:var(--text-base);line-height:var(--leading-normal);color:var(--text-primary);background-color:var(--bg-primary);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}@media (prefers-contrast: high){:root{--text-primary: #ffffff;--text-secondary: #ffffff;--bg-primary: #000000;--bg-secondary: #1a1a1a;--primary-500: #ffffff}}@media print{*{background:transparent!important;color:#000!important;box-shadow:none!important;text-shadow:none!important}body{font-size:12pt;line-height:1.4}h1,h2,h3,h4,h5,h6{page-break-after:avoid}blockquote,pre,tr,img{page-break-inside:avoid}img{max-width:100%!important}@page{margin:.5in}p,h2,h3{orphans:3;widows:3}}@font-face{font-family:iconfont;src:url(/assets/iconfont-PNS35LkI.eot?0ff28214377bfc74e5cf29455991154b?#iefix) format("embedded-opentype"),url(/assets/iconfont-C2045iXY.woff2?0ff28214377bfc74e5cf29455991154b) format("woff2"),url(/assets/iconfont-BeZampZj.woff?0ff28214377bfc74e5cf29455991154b) format("woff")}.hn{line-height:1}.hn:before{font-family:iconfont!important;font-style:normal;font-weight:400!important;vertical-align:top}.hn-android:before{content:""}.hn-angellist:before{content:""}.hn-apple:before{content:""}.hn-arweave:before{content:""}.hn-behance:before{content:""}.hn-bloomberg:before{content:""}.hn-bluesky:before{content:""}.hn-crunchbase:before{content:""}.hn-digg:before{content:""}.hn-discord:before{content:""}.hn-discourse:before{content:""}.hn-facebook-round:before{content:""}.hn-facebook-square:before{content:""}.hn-figma:before{content:""}.hn-giphy:before{content:""}.hn-github:before{content:""}.hn-golden:before{content:""}.hn-google-news:before{content:""}.hn-google:before{content:""}.hn-hackernoon:before{content:""}.hn-huggingface:before{content:""}.hn-imgur:before{content:""}.hn-instagram:before{content:""}.hn-ios:before{content:""}.hn-kaggle:before{content:""}.hn-linkedin:before{content:""}.hn-mastodon:before{content:""}.hn-minds:before{content:""}.hn-newsbreak:before{content:""}.hn-npm:before{content:""}.hn-open-ai:before{content:""}.hn-pinterest:before{content:""}.hn-podcasts:before{content:""}.hn-product-hunt:before{content:""}.hn-reddit:before{content:""}.hn-rss:before{content:""}.hn-sia:before{content:""}.hn-steam:before{content:""}.hn-threads:before{content:""}.hn-tiktok:before{content:""}.hn-twitch:before{content:""}.hn-twitter:before{content:""}.hn-unsplash:before{content:""}.hn-viewblocks:before{content:""}.hn-wikipedia:before{content:""}.hn-x:before{content:""}.hn-youtube:before{content:""}.hn-business:before{content:""}.hn-cloud:before{content:""}.hn-cybersecurity:before{content:""}.hn-data-science:before{content:""}.hn-finance:before{content:""}.hn-futurism:before{content:""}.hn-gaming:before{content:""}.hn-hackernoon-purcat:before{content:""}.hn-life-hacking:before{content:""}.hn-machine-learning:before{content:""}.hn-management:before{content:""}.hn-media:before{content:""}.hn-product-management:before{content:""}.hn-programming:before{content:""}.hn-remote:before{content:""}.hn-science:before{content:""}.hn-society:before{content:""}.hn-startups:before{content:""}.hn-tech-companies:before{content:""}.hn-tech-stories:before{content:""}.hn-technology:before{content:""}.hn-web3:before{content:""}.hn-writing:before{content:""}.hn-ad:before{content:""}.hn-align-center:before{content:""}.hn-align-justify:before{content:""}.hn-align-left:before{content:""}.hn-align-right:before{content:""}.hn-analytics:before{content:""}.hn-angle-down:before{content:""}.hn-angle-left:before{content:""}.hn-angle-right:before{content:""}.hn-angle-up:before{content:""}.hn-arrow-alt-circle-down:before{content:""}.hn-arrow-alt-circle-left:before{content:""}.hn-arrow-alt-circle-right:before{content:""}.hn-arrow-alt-circle-up:before{content:""}.hn-arrow-circle-down:before{content:""}.hn-arrow-circle-left:before{content:""}.hn-arrow-circle-right:before{content:""}.hn-arrow-circle-up:before{content:""}.hn-arrow-down:before{content:""}.hn-arrow-left:before{content:""}.hn-arrow-right:before{content:""}.hn-arrow-up:before{content:""}.hn-at:before{content:""}.hn-badge-check:before{content:""}.hn-bank:before{content:""}.hn-bars:before{content:""}.hn-bell-exclaimation:before{content:""}.hn-bell-mute:before{content:""}.hn-bell:before{content:""}.hn-bold:before{content:""}.hn-bolt:before{content:""}.hn-book-heart:before{content:""}.hn-bookmark:before{content:""}.hn-box-usd:before{content:""}.hn-brightness-high:before{content:""}.hn-brightness-low:before{content:""}.hn-bullet-list:before{content:""}.hn-bullhorn:before{content:""}.hn-calender:before{content:""}.hn-cc:before{content:""}.hn-chart-line:before{content:""}.hn-chart-network:before{content:""}.hn-check-box:before{content:""}.hn-check-circle:before{content:""}.hn-check-list:before{content:""}.hn-check:before{content:""}.hn-chevron-down:before{content:""}.hn-chevron-up:before{content:""}.hn-circle-notch:before{content:""}.hn-clipboard:before{content:""}.hn-clock:before{content:""}.hn-cloud-download-alt:before{content:""}.hn-cloud-upload:before{content:""}.hn-code-block:before{content:""}.hn-code:before{content:""}.hn-cog:before{content:""}.hn-comment-dots:before{content:""}.hn-comment-quote:before{content:""}.hn-comment:before{content:""}.hn-comments:before{content:""}.hn-copy:before{content:""}.hn-credit-card:before{content:""}.hn-crown:before{content:""}.hn-divider:before{content:""}.hn-download-alt:before{content:""}.hn-download:before{content:""}.hn-edit:before{content:""}.hn-ellipses-horizontal-circle:before{content:""}.hn-ellipses-horizontal:before{content:""}.hn-ellipses-vertical-circle:before{content:""}.hn-ellipses-vertical:before{content:""}.hn-envelope:before{content:""}.hn-exclaimation:before{content:""}.hn-exclamation-triangle:before{content:""}.hn-expand:before{content:""}.hn-external-link:before{content:""}.hn-eye-cross:before{content:""}.hn-eye:before{content:""}.hn-face-thinking:before{content:""}.hn-file-import:before{content:""}.hn-filter-alt-circle:before{content:""}.hn-filter:before{content:""}.hn-fire:before{content:""}.hn-flag-checkered:before{content:""}.hn-flag:before{content:""}.hn-folder-open:before{content:""}.hn-folder:before{content:""}.hn-globe-americas:before{content:""}.hn-globe:before{content:""}.hn-grid:before{content:""}.hn-h1:before{content:""}.hn-h2:before{content:""}.hn-h3:before{content:""}.hn-headphones:before{content:""}.hn-heart:before{content:""}.hn-highlight:before{content:""}.hn-hockey-mask:before{content:""}.hn-home:before{content:""}.hn-image:before{content:""}.hn-indent:before{content:""}.hn-info-circle:before{content:""}.hn-italics:before{content:""}.hn-lightbulb:before{content:""}.hn-line-height:before{content:""}.hn-link:before{content:""}.hn-location-pin:before{content:""}.hn-lock-alt:before{content:""}.hn-lock-open:before{content:""}.hn-lock:before{content:""}.hn-login:before{content:""}.hn-logout:before{content:""}.hn-message-dots:before{content:""}.hn-message:before{content:""}.hn-minus:before{content:""}.hn-moon:before{content:""}.hn-music:before{content:""}.hn-newspaper:before{content:""}.hn-numbered-list:before{content:""}.hn-octagon-check:before{content:""}.hn-octagon-times:before{content:""}.hn-outdent:before{content:""}.hn-page-break:before{content:""}.hn-paperclip:before{content:""}.hn-paragraph:before{content:""}.hn-pause:before{content:""}.hn-pen-nib:before{content:""}.hn-pen:before{content:""}.hn-pencil-ruler:before{content:""}.hn-pencil:before{content:""}.hn-people-carry:before{content:""}.hn-phone-ringing-high:before{content:""}.hn-phone-ringing-low:before{content:""}.hn-plane-departure:before{content:""}.hn-plane:before{content:""}.hn-play:before{content:""}.hn-playlist:before{content:""}.hn-plus:before{content:""}.hn-print:before{content:""}.hn-pro:before{content:""}.hn-question:before{content:""}.hn-quote-left:before{content:""}.hn-quote-right:before{content:""}.hn-receipt:before{content:""}.hn-refresh:before{content:""}.hn-retro-camera:before{content:""}.hn-robot:before{content:""}.hn-save:before{content:""}.hn-search:before{content:""}.hn-seedlings:before{content:""}.hn-share:before{content:""}.hn-shop:before{content:""}.hn-shopping-cart:before{content:""}.hn-shuffle:before{content:""}.hn-sort:before{content:""}.hn-sound-mute:before{content:""}.hn-sound-on:before{content:""}.hn-sparkles:before{content:""}.hn-spinner-third:before{content:""}.hn-spinner:before{content:""}.hn-star-crescent:before{content:""}.hn-star:before{content:""}.hn-strike-through:before{content:""}.hn-sun:before{content:""}.hn-table:before{content:""}.hn-tag:before{content:""}.hn-text-slash:before{content:""}.hn-themes:before{content:""}.hn-thumbsdown:before{content:""}.hn-thumbsup:before{content:""}.hn-thumbtack:before{content:""}.hn-times-circle:before{content:""}.hn-times:before{content:""}.hn-translate:before{content:""}.hn-trash-alt:before{content:""}.hn-trash:before{content:""}.hn-trending:before{content:""}.hn-trophy:before{content:""}.hn-underline:before{content:""}.hn-unlock-alt:before{content:""}.hn-unlock:before{content:""}.hn-upload-alt:before{content:""}.hn-upload:before{content:""}.hn-user-check:before{content:""}.hn-user-headset:before{content:""}.hn-user:before{content:""}.hn-users-crown:before{content:""}.hn-users:before{content:""}.hn-vote-yeah:before{content:""}.hn-wallet:before{content:""}.hn-window-close:before{content:""}.hn-ad-solid:before{content:""}.hn-align-center-solid:before{content:""}.hn-align-justify-solid:before{content:""}.hn-align-left-solid:before{content:""}.hn-align-right-solid:before{content:""}.hn-analytics-solid:before{content:""}.hn-angle-down-solid:before{content:""}.hn-angle-left-solid:before{content:""}.hn-angle-right-solid:before{content:""}.hn-angle-up-solid:before{content:""}.hn-arrow-alt-circle-down-solid:before{content:""}.hn-arrow-alt-circle-left-solid:before{content:""}.hn-arrow-alt-circle-right-solid:before{content:""}.hn-arrow-alt-circle-up-solid:before{content:""}.hn-arrow-circle-down-solid:before{content:""}.hn-arrow-circle-left-solid:before{content:""}.hn-arrow-circle-right-solid:before{content:""}.hn-arrow-circle-up-solid:before{content:""}.hn-arrow-down-solid:before{content:""}.hn-arrow-left-solid:before{content:""}.hn-arrow-right-solid:before{content:""}.hn-arrow-up-solid:before{content:""}.hn-at-solid:before{content:""}.hn-badge-check-solid:before{content:""}.hn-bank-solid:before{content:""}.hn-bars-solid:before{content:""}.hn-bell-exclaimation-solid:before{content:""}.hn-bell-mute-solid:before{content:""}.hn-bell-solid:before{content:""}.hn-bold-solid:before{content:""}.hn-bolt-solid:before{content:""}.hn-book-heart-solid:before{content:""}.hn-bookmark-solid:before{content:""}.hn-box-usd-solid:before{content:""}.hn-brightness-high-solid:before{content:""}.hn-brightness-low-solid:before{content:""}.hn-bullet-list-solid:before{content:""}.hn-bullhorn-solid:before{content:""}.hn-calender-solid:before{content:""}.hn-cc-solid:before{content:""}.hn-chart-line-solid:before{content:""}.hn-chart-network-solid:before{content:""}.hn-check-box-solid:before{content:""}.hn-check-circle-solid:before{content:""}.hn-check-list-solid:before{content:""}.hn-check-solid:before{content:""}.hn-chevron-down-solid:before{content:""}.hn-chevron-up-solid:before{content:""}.hn-circle-notch-solid:before{content:""}.hn-clipboard-solid:before{content:""}.hn-clock-solid:before{content:""}.hn-cloud-download-solid:before{content:""}.hn-cloud-upload-solid:before{content:""}.hn-code-block-solid:before{content:""}.hn-code-solid:before{content:""}.hn-cog-solid:before{content:""}.hn-comment-dots-solid:before{content:""}.hn-comment-quote-solid:before{content:""}.hn-comment-solid:before{content:""}.hn-comments-solid:before{content:""}.hn-copy-solid:before{content:""}.hn-credit-card-solid:before{content:""}.hn-crown-solid:before{content:""}.hn-divider-solid:before{content:""}.hn-download-alt-solid:before{content:""}.hn-download-solid:before{content:""}.hn-edit-solid:before{content:""}.hn-ellipses-horizontal-circle-solid:before{content:""}.hn-ellipses-horizontal-solid:before{content:""}.hn-ellipses-vertical-circle-solid:before{content:""}.hn-ellipses-vertical-solid:before{content:""}.hn-envelope-solid:before{content:""}.hn-exclaimation-solid:before{content:""}.hn-exclamation-triangle-solid:before{content:""}.hn-expand-solid:before{content:""}.hn-external-link-solid:before{content:""}.hn-eye-cross-solid:before{content:""}.hn-eye-solid:before{content:""}.hn-face-thinking-solid:before{content:""}.hn-file-import-solid:before{content:""}.hn-filter-alt-circle-solid:before{content:""}.hn-filter-solid:before{content:""}.hn-fire-solid:before{content:""}.hn-flag-checkered-solid:before{content:""}.hn-flag-solid:before{content:""}.hn-folder-open-solid:before{content:""}.hn-folder-solid:before{content:""}.hn-globe-americas-solid:before{content:""}.hn-globe-solid:before{content:""}.hn-grid-solid:before{content:""}.hn-heading-1-solid:before{content:""}.hn-heading-2-solid:before{content:""}.hn-heading-3-solid:before{content:""}.hn-headphones-solid:before{content:""}.hn-heart-solid:before{content:""}.hn-highlight-solid:before{content:""}.hn-hockey-mask-solid:before{content:""}.hn-home-solid:before{content:""}.hn-image-solid:before{content:""}.hn-indent-solid:before{content:""}.hn-info-circle-solid:before{content:""}.hn-italics-solid:before{content:""}.hn-lightbulb-solid:before{content:""}.hn-line-height-solid:before{content:""}.hn-link-solid:before{content:""}.hn-location-pin-solid:before{content:""}.hn-lock-alt-solid:before{content:""}.hn-lock-open-solid:before{content:""}.hn-lock-solid:before{content:""}.hn-login-solid:before{content:""}.hn-logout-solid:before{content:""}.hn-message-dots-solid:before{content:""}.hn-message-solid:before{content:""}.hn-minus-solid:before{content:""}.hn-moon-solid:before{content:""}.hn-music-solid:before{content:""}.hn-newspaper-solid:before{content:""}.hn-numbered-list-solid:before{content:""}.hn-octagon-check-solid:before{content:""}.hn-octagon-times-solid:before{content:""}.hn-outdent-solid:before{content:""}.hn-page-break-solid:before{content:""}.hn-paperclip-solid:before{content:""}.hn-paragraph-solid:before{content:""}.hn-pause-solid:before{content:""}.hn-pen-nib-solid:before{content:""}.hn-pen-solid:before{content:""}.hn-pencil-ruler-solid:before{content:""}.hn-pencil-solid:before{content:""}.hn-people-carry-solid:before{content:""}.hn-phone-ringing-high-solid:before{content:""}.hn-phone-ringing-low-solid:before{content:""}.hn-plane-departure-solid:before{content:""}.hn-plane-solid:before{content:""}.hn-play-solid:before{content:""}.hn-playlist-solid:before{content:""}.hn-plus-solid:before{content:""}.hn-print-solid:before{content:""}.hn-pro-solid:before{content:""}.hn-question-solid:before{content:""}.hn-quote-left-solid:before{content:""}.hn-quote-right-solid:before{content:""}.hn-receipt-solid:before{content:""}.hn-refresh-solid:before{content:""}.hn-retro-camera-solid:before{content:""}.hn-robot-solid:before{content:""}.hn-save-solid:before{content:""}.hn-search-solid:before{content:""}.hn-seedlings-solid:before{content:""}.hn-share-solid:before{content:""}.hn-shop-solid:before{content:""}.hn-shopping-cart-solid:before{content:""}.hn-shuffle-solid:before{content:""}.hn-sort-solid:before{content:""}.hn-sound-mute-solid:before{content:""}.hn-sound-on-solid:before{content:""}.hn-sparkles-solid:before{content:""}.hn-spinner-solid:before{content:""}.hn-spinner-third-solid:before{content:""}.hn-star-crescent-solid:before{content:""}.hn-star-solid:before{content:""}.hn-strike-through-solid:before{content:""}.hn-sun-solid:before{content:""}.hn-table-solid:before{content:""}.hn-tag-solid:before{content:""}.hn-text-slash-solid:before{content:""}.hn-themes-solid:before{content:""}.hn-thumbsdown-solid:before{content:""}.hn-thumbsup-solid:before{content:""}.hn-thumbtack-solid:before{content:""}.hn-times-circle-solid:before{content:""}.hn-times-solid:before{content:""}.hn-translate-solid:before{content:""}.hn-trash-alt-solid:before{content:""}.hn-trash-solid:before{content:""}.hn-trending-solid:before{content:""}.hn-trophy-solid:before{content:""}.hn-underline-solid:before{content:""}.hn-unlock-alt-solid:before{content:""}.hn-unlock-solid:before{content:""}.hn-upload-alt-solid:before{content:""}.hn-upload-solid:before{content:""}.hn-user-check-solid:before{content:""}.hn-user-headset-solid:before{content:""}.hn-user-solid:before{content:""}.hn-users-crown-solid:before{content:""}.hn-users-solid:before{content:""}.hn-vote-yeah-solid:before{content:""}.hn-wallet-solid:before{content:""}.hn-window-close-solid:before{content:""}*{box-sizing:border-box;margin:0;padding:0}html{scroll-behavior:smooth;height:100%}body{font-family:var(--font-primary);font-size:var(--text-base);line-height:var(--leading-normal);color:var(--text-primary);background-color:var(--bg-primary);-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;height:100%;overflow-x:hidden}#root{height:100%;min-height:100vh}.app{min-height:100vh;display:flex;flex-direction:column}h1,h2,h3,h4,h5,h6{font-family:var(--font-display);font-weight:var(--font-semibold);line-height:var(--leading-tight);color:var(--text-primary)}h1{font-size:var(--text-4xl)}h2{font-size:var(--text-3xl)}h3{font-size:var(--text-2xl)}h4{font-size:var(--text-xl)}h5{font-size:var(--text-lg)}h6{font-size:var(--text-base)}p{line-height:var(--leading-relaxed);color:var(--text-secondary)}a{color:var(--accent-500);text-decoration:none;transition:color var(--transition-normal) var(--ease-in-out)}a:hover{color:var(--accent-400)}a:focus{outline:2px solid var(--accent-500);outline-offset:2px;border-radius:var(--radius-sm)}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0,0,0,0);white-space:nowrap;border:0}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.flex{display:flex}.flex-col{flex-direction:column}.items-center{align-items:center}.justify-center{justify-content:center}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:var(--primary-600)}::-webkit-scrollbar-thumb{background:var(--primary-500);border-radius:var(--radius-full)}::-webkit-scrollbar-thumb:hover{background:var(--accent-500)}::selection{background-color:var(--selection-bg);color:var(--selection-color)}::-moz-selection{background-color:var(--selection-bg);color:var(--selection-color)}:focus{outline:2px solid var(--accent-500);outline-offset:var(--focus-ring-offset)}:focus:not(:focus-visible){outline:none}@media (prefers-reduced-motion: reduce){*{animation-duration:.01ms!important;animation-iteration-count:1!important;transition-duration:.01ms!important}html{scroll-behavior:auto}}@media (prefers-contrast: high){.card,.btn{border-width:2px}}@media print{*{background:transparent!important;color:#000!important;box-shadow:none!important;text-shadow:none!important}body{font-size:12pt;line-height:1.4}.btn{display:none}}.icon-examples{display:none}:root{--color-primary: var(--accent-500);--color-text-primary: var(--text-primary);--color-text-secondary: var(--text-secondary);--color-background-primary: var(--bg-primary);--color-background-secondary: var(--bg-secondary);--color-background-tertiary: var(--bg-tertiary)}@media (max-width: 768px){html{height:100vh;height:100dvh;overflow:hidden;background:var(--bg-primary)}body{height:100vh;height:100dvh;overflow:hidden;margin:0;padding:0;background:var(--bg-primary)}@supports (-webkit-touch-callout: none){body{height:-webkit-fill-available}}#root{height:100vh;height:100dvh;overflow:hidden}.layout.mobile-layout{height:100vh;height:100dvh;display:flex;flex-direction:column;overflow:hidden;background:var(--bg-primary)}.page-header{position:fixed!important;top:0!important;left:0!important;right:0!important;z-index:var(--z-fixed)!important;height:50px!important;min-height:50px!important;padding:0 16px 4px!important;background:var(--bg-primary)!important;border-bottom:1px solid var(--border-color)!important;backdrop-filter:blur(20px)!important;-webkit-backdrop-filter:blur(20px)!important;will-change:transform!important;transform:translateZ(0)!important}.page-header .dashboard-header-content{display:flex!important;align-items:center!important;justify-content:space-between!important;width:100%!important;min-height:44px!important;gap:12px!important;position:relative!important;z-index:1!important;padding-top:8px!important}.page-header .dashboard-date-info{flex:1!important;min-width:0!important}.page-header .dashboard-date-info .dashboard-title{font-size:16px!important;margin:0 0 1px!important;color:var(--text-primary)!important;font-weight:700!important;white-space:nowrap!important;overflow:hidden!important;text-overflow:ellipsis!important;line-height:1.2!important}.page-header .dashboard-date-info .dashboard-subtitle{font-size:12px!important;margin:0!important;color:var(--text-secondary)!important;line-height:1.2!important}.page-header .header-actions{flex-shrink:0!important}.main-content{flex:1;display:flex;flex-direction:column;overflow:hidden;padding-top:50px!important}.main-content.with-bottom-nav{padding-bottom:60px}.page-content{flex:1;overflow-y:auto;-webkit-overflow-scrolling:touch;padding:16px;min-height:max-content;height:auto;position:relative}.bottom-navigation{position:fixed;bottom:0;left:0;right:0;z-index:var(--z-sticky);height:60px;background:var(--bg-surface);border-top:1px solid var(--border-color);backdrop-filter:blur(10px);-webkit-backdrop-filter:blur(10px);box-shadow:0 -2px 10px #0000001a;will-change:transform;transform:translateZ(0)}.bottom-navigation .bottom-nav-container{display:flex;height:50px;align-items:center;justify-content:space-around;padding:0 var(--space-2, .5rem);position:relative;margin-top:5px}.bottom-navigation .bottom-nav-item{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:var(--space-2, .5rem);background:none;border:none;color:var(--text-secondary);cursor:pointer;border-radius:var(--radius-md, 6px);transition:all .2s ease;position:relative;min-width:60px;gap:.25rem;flex:1;max-width:80px;min-height:44px}.bottom-navigation .bottom-nav-item:hover:not(.active){color:var(--text-primary)}.bottom-navigation .bottom-nav-item.active{color:var(--accent-500)}.bottom-navigation .bottom-nav-item.active .bottom-nav-icon .hn{transform:scale(1.1);color:var(--accent-500)}.bottom-navigation .bottom-nav-icon{display:flex;align-items:center;justify-content:center;margin-bottom:4px}.bottom-navigation .bottom-nav-icon .hn{transition:all var(--transition-normal);font-size:18px;width:22px;height:22px;display:flex;align-items:center;justify-content:center}.bottom-navigation .bottom-nav-label{font-size:.75rem;font-weight:500;line-height:1;text-align:center;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;max-width:100%}.dashboard-v2__chat-button{position:fixed;right:16px;z-index:var(--z-popover);bottom:76px;background:linear-gradient(135deg,#4f46e5,#7c3aed);color:#fff;border:none;border-radius:50px;padding:12px 20px;display:flex;align-items:center;gap:8px;font-size:14px;font-weight:600;cursor:pointer;box-shadow:0 8px 24px #4f46e54d;transition:all .3s ease;min-height:44px}.dashboard-v2__chat-button:hover{transform:translateY(-2px);box-shadow:0 12px 32px #4f46e566}.dashboard-v2__chat-button:active{transform:translateY(0)}}@supports (-webkit-touch-callout: none){@media (max-width: 768px){.page-header{height:calc(env(safe-area-inset-top,44px) + 38px)!important;min-height:calc(env(safe-area-inset-top,44px) + 38px)!important;padding-top:env(safe-area-inset-top,44px)!important;background:var(--bg-primary)!important}.page-header .dashboard-header-content{height:38px!important;display:flex!important;align-items:center!important;justify-content:space-between!important;width:100%!important;gap:12px!important;position:relative!important;z-index:1!important;padding-top:0!important}.main-content{padding-top:calc(env(safe-area-inset-top,44px) + 38px)!important}.main-content.with-bottom-nav{padding-bottom:calc(60px + env(safe-area-inset-bottom,0px))}.bottom-navigation{height:calc(60px + env(safe-area-inset-bottom,0px))!important}.dashboard-v2__chat-button{bottom:calc(76px + env(safe-area-inset-bottom,0px))!important}}@media (max-width: 375px){.page-header{height:calc(env(safe-area-inset-top,44px) + 36px)!important}.page-header .dashboard-header-content{height:36px!important}.page-header .dashboard-header-content .dashboard-title{font-size:15px!important}.page-header .dashboard-header-content .dashboard-subtitle{font-size:11px!important}.main-content{padding-top:calc(env(safe-area-inset-top,44px) + 36px)!important}}@media (min-width: 376px) and (max-width: 413px){.page-header{height:calc(env(safe-area-inset-top,44px) + 38px)!important}.page-header .dashboard-header-content{height:38px!important}.main-content{padding-top:calc(env(safe-area-inset-top,44px) + 38px)!important}}@media (min-width: 414px) and (max-width: 430px){.page-header{height:calc(env(safe-area-inset-top,44px) + 40px)!important}.page-header .dashboard-header-content{height:40px!important}.page-header .dashboard-header-content .dashboard-title{font-size:17px!important}.main-content{padding-top:calc(env(safe-area-inset-top,44px) + 40px)!important}}@media (min-width: 431px){.page-header{height:calc(env(safe-area-inset-top,44px) + 42px)!important}.page-header .dashboard-header-content{height:42px!important}.page-header .dashboard-header-content .dashboard-title{font-size:17px!important}.page-header .dashboard-header-content .dashboard-subtitle{font-size:13px!important}.main-content{padding-top:calc(env(safe-area-inset-top,44px) + 42px)!important}}}@media (max-width: 768px) and (orientation: landscape){.page-header{height:44px!important;padding-bottom:2px!important}.page-header .dashboard-header-content{min-height:38px!important;padding-top:4px!important}.main-content{padding-top:44px!important}.main-content.with-bottom-nav{padding-bottom:50px}.bottom-navigation{height:50px}.bottom-navigation .bottom-nav-container{height:40px;margin-top:5px}.bottom-navigation .bottom-nav-item{padding:var(--space-1, .25rem);min-height:36px}.bottom-navigation .bottom-nav-label{font-size:.7rem}.dashboard-v2__chat-button{bottom:62px;padding:8px 16px}}@supports (-webkit-touch-callout: none){@media (max-width: 768px) and (orientation: landscape){.page-header{height:calc(env(safe-area-inset-top,20px) + 34px)!important;padding-top:env(safe-area-inset-top,20px)!important}.page-header .dashboard-header-content{height:34px!important;display:flex!important;align-items:center!important;justify-content:space-between!important;padding-top:0!important}.main-content{padding-top:calc(env(safe-area-inset-top,20px) + 34px)!important}.main-content.with-bottom-nav{padding-bottom:calc(50px + env(safe-area-inset-bottom,0px))}.bottom-navigation{height:calc(50px + env(safe-area-inset-bottom,0px))!important}.dashboard-v2__chat-button{bottom:calc(62px + env(safe-area-inset-bottom,0px))!important}}}@media (max-width: 480px){.page-header{padding-left:12px!important;padding-right:12px!important}.page-header .dashboard-header-content{gap:8px!important}.page-header .dashboard-date-info .dashboard-title{font-size:14px!important}.page-header .dashboard-date-info .dashboard-subtitle{font-size:11px!important}.page-content{padding:12px}.bottom-navigation .bottom-nav-item{min-width:50px;padding:var(--space-1, .25rem)}.bottom-navigation .bottom-nav-item .bottom-nav-icon .hn{font-size:16px;width:20px;height:20px}.bottom-navigation .bottom-nav-label{font-size:.65rem}.dashboard-v2__chat-button{right:12px;padding:10px 16px;font-size:13px}.dashboard-v2__chat-button .chat-icon{font-size:14px}}@media (max-width: 768px){.theme-light .page-header{background:#fff!important;border-bottom-color:#e2e8f0!important}.theme-light .page-header .dashboard-title{color:#1e293b!important}.theme-light .page-header .dashboard-subtitle{color:#475569!important}.theme-light .bottom-navigation{background:#fff;border-top-color:#e2e8f0}.theme-light .bottom-navigation .bottom-nav-item{color:#6b7280}.theme-light .bottom-navigation .bottom-nav-item:hover:not(.active){color:#1e293b}.theme-light .bottom-navigation .bottom-nav-item.active{color:var(--accent-500)}.theme-dark .page-header{background:#0f172a!important;border-bottom-color:#374151!important}.theme-dark .page-header .dashboard-title{color:#f8fafc!important}.theme-dark .page-header .dashboard-subtitle{color:#cbd5e1!important}.theme-dark .bottom-navigation{background:#1e293b;border-top-color:#374151}.theme-dark .bottom-navigation .bottom-nav-item{color:#cbd5e1}.theme-dark .bottom-navigation .bottom-nav-item:hover:not(.active){color:#f8fafc}.theme-dark .bottom-navigation .bottom-nav-item.active{color:var(--accent-500)}}@media (max-width: 768px){.debug-ios-layout-fix .page-header{background:#0f03;border:1px solid lime}.debug-ios-layout-fix .page-header:before{content:"Header (Web固定50px)";position:absolute;top:2px;left:2px;color:#000;font-size:10px;font-weight:700;background:#ffffffe6;padding:2px 4px;border-radius:2px;z-index:999}.debug-ios-layout-fix .main-content{background:#00ff000d;border:1px solid green}.debug-ios-layout-fix .page-content{background:#0000ff0d;border:1px solid blue}.debug-ios-layout-fix .bottom-navigation{background:#ff03;border:1px solid yellow}.debug-ios-layout-fix .bottom-navigation:before{content:"Navigation (环境适应)";position:absolute;top:2px;left:2px;color:#000;font-size:10px;font-weight:700;background:#ffffffe6;padding:2px 4px;border-radius:2px;z-index:999}.debug-ios-layout-fix .dashboard-v2__chat-button{background:#ff00ff4d;border:1px solid magenta}.debug-ios-layout-fix .dashboard-v2__chat-button:before{content:"Chat (Multi-Env)";position:absolute;top:-18px;left:0;color:#000;font-size:8px;background:#ffffffe6;padding:1px 3px;border-radius:2px;white-space:nowrap}}@supports (-webkit-touch-callout: none){@media (max-width: 768px){.debug-ios-layout-fix .page-header:before{content:"Header (高度已调整+居中)"!important}.debug-ios-layout-fix .bottom-navigation:before{content:"Navigation (iOS适应)"!important}}}@media (max-width: 768px) and (prefers-reduced-motion: reduce){.page-header,.bottom-navigation,.dashboard-v2__chat-button{transition:none;backdrop-filter:none;-webkit-backdrop-filter:none}}@media (max-width: 768px) and (prefers-contrast: high){.page-header{border-bottom-width:2px}.bottom-navigation{border-top-width:2px}}
